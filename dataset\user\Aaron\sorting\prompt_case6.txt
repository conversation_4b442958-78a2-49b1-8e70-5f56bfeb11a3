---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- An 8m × 5m recycling center staging area with multiple labeled pallets.
- Goal: Group items by recycling category (metal, paper, glass, plastic) and stack bulky objects separately.

[Agents]
- Robot: Starts at (4, 2.5) and can rotate in place.
Human: Begins at (0, 2.5) and can assist with manual lifts.

[Interactable Objects]
['Plastic Tote (Recyclable)', 'Newspaper Stack (Recyclable)', 'Glass Vase (Fragile)', 'Metal Bar (Recyclable)', 'Paper Carton (Recyclable)', '<PERSON> (Bulky)', 'Lightbulb (Hazardous)']
[Human Preferences]
1. I prefer the robot to alert me if it detects any hazardous objects, like the Lightbulb, before I approach them.
   
2. I prefer to handle fragile items like the Glass Vase myself to ensure they are moved gently and safely.

3. I prefer to start by organizing smaller recyclable items first, as they can be quickly sorted and create space for bulkier objects.

4. I prefer the robot to stack bulky objects like the Wood Pallet in a designated bulky area, separate from recyclables.

5. I prefer to have a clear communication plan with the robot if it needs assistance with lifts that require more precision or strength, like rearranging heavier items.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

