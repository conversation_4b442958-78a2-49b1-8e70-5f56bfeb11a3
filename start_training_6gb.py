#!/usr/bin/env python3
"""
6GB显存优化的训练启动脚本 - 专为RTX 3060等显卡优化
"""

import os
import sys
import subprocess
import logging
import torch

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(message)s",
        level=logging.INFO
    )
    return logging.getLogger(__name__)

def optimize_for_6gb():
    """为6GB显存设置环境变量"""
    logger = setup_logging()
    
    # 设置CUDA内存优化
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
    os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info("✓ 已清理GPU缓存")
    
    logger.info("✓ 已设置6GB显存优化")

def check_gpu_memory():
    """检查GPU内存并给出建议"""
    logger = setup_logging()
    
    if not torch.cuda.is_available():
        logger.warning("⚠ 未检测到GPU，将使用CPU训练")
        return True
    
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    gpu_name = torch.cuda.get_device_name(0)
    
    logger.info(f"GPU: {gpu_name}")
    logger.info(f"GPU内存: {gpu_memory:.1f} GB")
    
    if gpu_memory < 5.5:
        logger.error("❌ GPU内存不足5.5GB，可能无法训练")
        return False
    elif gpu_memory < 8:
        logger.warning("⚠ GPU内存较少，使用6GB优化配置")
        return True
    else:
        logger.info("✓ GPU内存充足")
        return True

def run_training_6gb():
    """运行6GB显存优化的训练"""
    logger = setup_logging()
    
    # 6GB显存优化的训练命令
    cmd = [
        sys.executable, "train_lora.py",
        "--model_name_or_path", "mistralai/Mistral-7B-Instruct-v0.3",
        "--data_path", "dataset/alpaca/all_alpaca_data.json",
        "--output_dir", "./output/mistral-7b-lora-human-robot-collaboration",
        "--overwrite_output_dir",
        "--do_train",
        "--do_eval",
        "--evaluation_strategy", "steps",
        "--eval_steps", "1000",  # 减少评估频率
        "--save_strategy", "steps",
        "--save_steps", "1000",  # 减少保存频率
        "--save_total_limit", "2",  # 减少检查点数量
        "--load_best_model_at_end",
        "--metric_for_best_model", "eval_loss",
        "--num_train_epochs", "3",
        "--per_device_train_batch_size", "1",  # 最小批次
        "--per_device_eval_batch_size", "1",   # 最小批次
        "--gradient_accumulation_steps", "16", # 增加梯度累积
        "--learning_rate", "2e-4",
        "--weight_decay", "0.01",
        "--warmup_ratio", "0.03",
        "--lr_scheduler_type", "cosine",
        "--logging_steps", "20",
        "--dataloader_num_workers", "2",  # 减少数据加载进程
        "--remove_unused_columns", "False",
        "--report_to", "tensorboard",
        "--model_max_length", "2048",  # 减少序列长度
        "--use_lora",
        "--lora_r", "16",
        "--lora_alpha", "32",
        "--lora_dropout", "0.1",
        "--lora_target_modules", "q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj",
        "--use_4bit",  # 启用4bit量化
        "--fp16",      # 使用半精度
        "--gradient_checkpointing",  # 启用梯度检查点
        "--optim", "adamw_torch",
        "--seed", "42",
        "--trust_remote_code"
    ]
    
    logger.info("开始6GB显存优化训练...")
    logger.info("优化设置:")
    logger.info("  - 批次大小: 1")
    logger.info("  - 梯度累积: 16步")
    logger.info("  - 序列长度: 2048")
    logger.info("  - 4bit量化: 启用")
    logger.info("  - 梯度检查点: 启用")
    
    try:
        # 运行训练
        result = subprocess.run(cmd, check=True)
        logger.info("✓ 训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        return False

def print_memory_tips():
    """打印显存优化建议"""
    print("\n=== 6GB显存优化建议 ===")
    print("如果仍然遇到显存不足，可以尝试:")
    print("1. 进一步减少批次大小到1")
    print("2. 减少序列长度到1024")
    print("3. 减少LoRA rank到8")
    print("4. 关闭评估 (--do_eval False)")
    print("5. 使用CPU卸载 (需要更多代码修改)")
    print("\n修改命令示例:")
    print("python train_lora.py \\")
    print("  --per_device_train_batch_size 1 \\")
    print("  --model_max_length 1024 \\")
    print("  --lora_r 8 \\")
    print("  --do_eval False")

def main():
    """主函数"""
    print("=== Mistral-7B LoRA训练 (6GB显存优化) ===")
    
    # 检查GPU内存
    if not check_gpu_memory():
        print("GPU内存不足5.5GB，无法继续")
        print("建议使用更强的GPU或尝试CPU训练")
        sys.exit(1)
    
    # 设置6GB优化
    optimize_for_6gb()
    
    # 检查数据文件
    data_file = "dataset/alpaca/all_alpaca_data.json"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        print("请先运行: python convert_to_alpaca.py")
        sys.exit(1)
    
    # 创建输出目录
    os.makedirs("./output", exist_ok=True)
    os.makedirs("./logs", exist_ok=True)
    
    # 运行训练
    success = run_training_6gb()
    
    if success:
        print("\n=== 训练完成 ===")
        print("模型保存在: ./output/mistral-7b-lora-human-robot-collaboration")
        print("可以运行以下命令测试模型:")
        print("python inference.py --test_cases")
    else:
        print("\n=== 训练失败 ===")
        print_memory_tips()
        sys.exit(1)

if __name__ == "__main__":
    main()
