---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 10m × 8m hospital corridor with tiled floors and several supply cabinets. Hygiene standards are critical.
- Goal: Sanitize floors, disinfect high-touch surfaces, and restock cleaning supplies in cabinets.

[Agents]
- Human: Starts at (2,4). Robot: Starts at (8,7). The robot must not block emergency paths.

[Interactable Objects]
['Disinfectant Spray', 'Mop', 'Disposable Gloves', 'Trash Bin', 'Hand Sanitizer Bottles', 'Floor Squeegee', 'Microfiber Mop Head']
[Human Preferences]
1. I prefer to sanitize high-touch surfaces first, as it helps prevent germ spread while moving through the corridor.

2. I prefer to restock cleaning supplies in the cabinets after disinfecting surfaces and before mopping the floors to maintain hygiene standards during the process.

3. I prefer to use a microfiber mop head to ensure thorough cleanliness when cleaning the tiled floors.

4. I prefer to handle any trash disposal tasks after using the floor cleaning tools to keep the environment tidy throughout the process.

5. I prefer to wear disposable gloves when starting the task and change them if they get contaminated to maintain personal and environmental hygiene.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

