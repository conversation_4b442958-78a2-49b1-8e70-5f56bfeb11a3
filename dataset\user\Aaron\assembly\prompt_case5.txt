---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5 m × 5 m workstation with a central safety enclosure.
- Goal: Assemble a large sensor tower at coordinate (2,3).

[Agents]
- Robot: Positioned at (0,5), restricted to the top edge. Human: Positioned at (5,0), restricted to the right edge.

[Interactable Objects]
['Tower Base', 'Sensor Array', 'Battery Module', 'Control Console', 'Support Beam', 'Antenna Array', 'Power Converter']
[Human Preferences]
1. I prefer to start assembling with the Tower Base #1, as it sets a stable foundation for the sensor tower and is closer to my position.

2. I prefer the robot to assist by first handing me the Sensor Array #2, since it is located near both the Control Console #4 and the Power Converter #7, allowing for a more efficient workflow.

3. I prefer to pick up the Antenna Array #6 after completing the base assembly, as it is within easy reach from my position and will keep the workspace organized by clearing the right edge early on.

4. I prefer the robot to alert me by beeping twice before placing the Battery Module #3, ensuring that I am ready to receive and integrate it without delays.

5. I prefer to have the Support Beam #5 assembled last, as it will allow me to focus on more intricate parts first, considering its position on the opposite side from me.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

