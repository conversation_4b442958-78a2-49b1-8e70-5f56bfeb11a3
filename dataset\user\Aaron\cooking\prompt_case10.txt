---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A modern 8m × 4m test kitchen with high-tech appliances. Sous vide station at (7,3), storage closet at (6.5,0.5).
- Goal: Prepare sous vide steak and vegetables by gathering all necessary items near the sous vide station.

[Agents]
- Human: Starts at (1,3). Robot: Starts at (6,2). Both can navigate freely around the island at (4,2).

[Interactable Objects]
['Steak Cuts', 'Vegetable Pack', 'Sous Vide Bag', 'Tongs', 'Seasoning Jar', 'Vacuum Sealer', 'Marinade Bowl']
[Human Preferences]
1. I prefer to gather all necessary items, starting with those farthest away and moving toward those closest to the sous vide station.

2. I prefer to pick up lighter items myself, leaving the heavier items for the robot to handle and place in corners as per my general preference.

3. I prefer to work on preparing the vegetables first while the robot prepares the steak, to optimize parallel task execution.

4. I prefer to have the robot bring the Vacuum Sealer (#6) and Sous Vide Bag (#3) near the sous vide station first, as they are essential for sous vide preparation.

5. I prefer to maintain a clear path around the island, ensuring that neither I nor the robot obstructs each other's movement.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

