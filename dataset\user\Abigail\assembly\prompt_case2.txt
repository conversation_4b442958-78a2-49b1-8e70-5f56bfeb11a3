---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 3 m × 3 m workstation with labeled assembly corners.
- Goal: Assemble a small robotic arm at coordinate (1,2).

[Agents]
- Robot: Positioned at (3,0), can move along the perimeter. Human: Positioned at (0,3), can move only along diagonal walkways.

[Interactable Objects]
['Arm Base', 'Joint Motor', 'Sensor Module', 'Control Circuit', 'Mini Battery', 'Torque Amplifier', 'Rotation Sensor']
[Human Preferences]
1. I prefer to gather nearby components like the Arm Base #1 and Joint Motor #2 myself, as they are closer to my starting position, while asking the robot to handle items farther away.

2. I want the robot to bring me multiple items at once if it's efficient, but only if it can carry them safely without risking damage to any fragile components.

3. I prefer the robot to organize items in the order they are needed for assembly and provide me with real-time updates on their status so I can plan my movements efficiently.

4. I want to minimize movement by coordinating with the robot to ensure we are both utilizing our maximum efficiency, with me focusing on assembling and the robot on fetching.

5. I prefer to work on one corner of the workstation while the robot supplies parts in an adjacent corner to minimize crossover and potential interference.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

