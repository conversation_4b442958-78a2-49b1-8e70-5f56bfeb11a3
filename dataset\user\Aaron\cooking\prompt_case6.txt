---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A modern 5m × 5m open-concept kitchen-living area with a large island in the center and a sink at (2.5,2.5).
- Goal: Wash and organize fruits and vegetables on the island for meal prep.

[Agents]
- Human: Starts at (4,4). Robot: Starts at (0.5,0.5). Both must navigate around the island.

[Interactable Objects]
['Apple', 'Banana Bunch', 'Carrot Bundle', 'Colander', 'Chef Knife', 'Cutting Board', 'Vegetable Peeler']
[Human Preferences]
1. I prefer to pick up and wash all fruits and vegetables at the sink before organizing them on the island to minimize movement around the kitchen.

2. I prefer the robot to assist by bringing the cutting board close to the sink after I've finished washing the fruits and vegetables.

3. I prefer to have the colander positioned at the sink to easily place the washed items without needing to carry them over any distance.

4. I prefer to use the chef knife and vegetable peeler in sequence, so they should be easily accessible near each other on the island for efficient prep work.

5. I prefer the robot to inform me if any fruits or vegetables are missing from the designated prep area before I start cutting or peeling to ensure I can address it immediately.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

