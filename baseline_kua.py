import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from openai import OpenAI
import openai
import tqdm
import json
from utils import check_complete_userdata, load_previous_steps, generate_task_description
from get_response import get_response
import time
import argparse
import ast
import re

user_name_list = check_complete_userdata()
print(user_name_list)
# user_name_list = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
scenarios = ["assembly", "cooking", "sorting", "cleaning"]
prompt_dict = json.load(open("prompts.json", encoding="utf-8"))

def get_response_with_retry(prompt, api_server, max_retries=3, delay=2):
    for attempt in range(max_retries):
        try:
            response = get_response(prompt, api_server=api_server)
            return response
        except Exception as e:
            print(f"API error: {e}, retrying in {delay} seconds... (attempt {attempt + 1}/{max_retries})")
            time.sleep(delay)
    print(f"Failed after {max_retries} attempts, returning default response.")
    return "0.5"

def learn_user_preferences(name, scenario, api_server):
    """学习用户在特定场景下的偏好模式"""
    learned_preferences = []
    
    # 从训练集（所有任务）中学习
    for i in range(1, 11):  # 训练集：任务1-10
        try:
            # 加载用户数据
            steps = load_previous_steps(name, scenario, i)
            scenario_data = json.load(open(f"dataset/{scenario}.json"))
            
            # 分析用户的执行模式（不加载偏好文件）
            user_patterns = analyze_user_patterns(steps)
            learned_preferences.append(user_patterns)
            
        except Exception as e:
            print(f"Error learning from case {i}: {e}")
            continue
    
    # 总结学习到的偏好
    if learned_preferences:
        summary_prompt = "Based on the following user behavior patterns, summarize the key preferences and characteristics of this user:\n\n"
        for i, pattern in enumerate(learned_preferences):
            summary_prompt += f"Task {i+1}:\n{pattern}\n\n"
        
        summary_prompt += "Please provide a concise summary of this user's key preferences and behavioral patterns."
        
        try:
            summary = get_response_with_retry(summary_prompt, api_server=api_server)
            return summary
        except Exception as e:
            print(f"Error generating summary: {e}")
            return "No specific preferences learned."
    
    return "No training data available."

def analyze_user_patterns(steps):
    """分析用户的执行模式（不依赖偏好文件）"""
    analysis = f"Total Steps: {len(steps)}\n"
    
    # 统计执行者分布
    human_steps = sum(1 for step in steps if step.get("executor") == "Human")
    robot_steps = sum(1 for step in steps if step.get("executor") == "Robot")
    
    analysis += f"Human Steps: {human_steps}, Robot Steps: {robot_steps}\n"
    
    # 分析动作类型
    action_types = {}
    for step in steps:
        action = step.get("action", "")
        if action:
            action_type = action.split()[0] if action.split() else "Unknown"
            action_types[action_type] = action_types.get(action_type, 0) + 1
    
    analysis += f"Action Types: {action_types}\n"
    
    # 分析执行顺序和模式
    executor_sequence = [step.get("executor", "Unknown") for step in steps]
    analysis += f"Executor Sequence: {executor_sequence}\n"
    
    # 分析动作内容模式
    human_actions = [step.get("action", "") for step in steps if step.get("executor") == "Human"]
    robot_actions = [step.get("action", "") for step in steps if step.get("executor") == "Robot"]
    
    analysis += f"Human Actions: {human_actions}\n"
    analysis += f"Robot Actions: {robot_actions}\n"
    
    return analysis

class MCTSNode:
    def __init__(self, state, previous_steps, preference, learned_preferences=None, depth=0):
        self.state = state
        self.previous_steps = previous_steps
        self.preference = preference
        self.learned_preferences = learned_preferences
        self.depth = depth
        self.children = []
        self.value = 0
        self.visits = 0

    def expand(self, scenario_data, scenario, case_id, api_server, beam_width=3, step2_length=0):
        # LLM一次生成多个step2完整动作序列
        prompt = generate_task_description(scenario_data, case_id)
        if self.preference:
            prompt += "\n\n [REAL HUMAN PREFERENCE - PRIMARY]" + self.preference
        if self.learned_preferences:
            prompt += f"\n\n [learned preferences]\n{self.learned_preferences}"
        prompt += prompt_dict["step2_prediction_prompt"]
        prompt += f"\nprevious steps: {self.previous_steps}\n"
        if step2_length:
            prompt += f"\n请确保生成的动作序列长度为{step2_length}。"
        responses = []
        for _ in range(beam_width):
            response = get_response_with_retry(prompt, api_server=api_server)
            # 修正正则
            match = re.search(r"```(?:python)?\s*([\s\S]*?)```", response)
            if match:
                code_str = match.group(1).strip()
            else:
                code_str = response.strip()  # fallback
            try:
                seq = ast.literal_eval(code_str)
                if isinstance(seq, list) and all(isinstance(x, dict) and 'executor' in x and 'action' in x for x in seq):
                    responses.append(seq)
            except Exception:
                continue
        for seq in responses:
            self.children.append(MCTSNode(self.state, self.previous_steps + seq, self.preference, self.learned_preferences, self.depth + 1))

    def simulate(self, scenario_data, scenario, case_id, api_server, rollout_steps=3, step2_length=0):
        # 对完整step2序列进行评估
        sim_steps = self.previous_steps.copy()
        if step2_length:
            seq = self.previous_steps[-step2_length:]
        else:
            seq = self.previous_steps
        # 使用LLM评估整个step2序列
        eval_prompt = prompt_dict.get("action_evaluation", {}).get("step2_eval_prompt", "请对以下step=2动作序列的合理性和完成度进行评分，满分1分，最低0分：\n")
        eval_prompt += f"\nTask Goal: {scenario_data['cases'][str(case_id)]['goal']}"
        eval_prompt += f"\nPrevious Steps: {sim_steps[:-len(seq)] if len(seq)<len(sim_steps) else []}"
        eval_prompt += f"\nProposed step2 sequence: {seq}"
        if self.preference:
            eval_prompt += f"\nHuman Preference: {self.preference}"
        else:
            eval_prompt += "\nNo specific human preferences provided."
        quality_response = get_response_with_retry(eval_prompt, api_server=api_server)
        try:
            quality_score = float(quality_response.strip())
        except Exception:
            quality_score = 0.5
        return quality_score

    def backpropagate(self, value):
        self.visits += 1
        self.value += value

def mcts(root, scenario_data, scenario, case_id, api_server, iterations=1, beam_width=3, rollout_steps=3, step2_length=0):
    root.expand(scenario_data, scenario, case_id, api_server, beam_width, step2_length)
    if not root.children:
        print("Warning: No valid step2 sequence generated by LLM.")
        return []
    for child in root.children:
        value = child.simulate(scenario_data, scenario, case_id, api_server, rollout_steps, step2_length)
        child.backpropagate(value)
    best_child = max(root.children, key=lambda n: n.value / (n.visits + 1e-5))
    if step2_length:
        return best_child.previous_steps[-step2_length:]
    else:
        return best_child.previous_steps

def test_with_mcts(name, scenario, learned_preferences, rollout_steps, use_preference=True, global_correct=0, global_total=0):
    correct_count = 0
    total_count = 0
    for i in range(1, 11):  # 测试集：任务1-10（cleaning场景的所有任务）
        try:
            steps = load_previous_steps(name, scenario, i)
            scenario_data = json.load(open(f"dataset/{scenario}.json"))
            # step=2动作序列的起止
            step2_start = [idx for idx, s in enumerate(steps) if s.get('step', None) == 2]
            if not step2_start:
                print(f"No step=2 in case {i}")
                continue
            step2_indices = step2_start
            step2_length = len(step2_indices)
            previous_steps = steps[:step2_indices[0]]
            target_step2_seq = steps[step2_indices[0]:step2_indices[0]+step2_length]
            if use_preference:
                with open(f"dataset/user/{name}/{scenario}/preference_case{i}.txt", "r", encoding="utf-8") as file:
                    preference = file.read()
                preference = f"[REAL HUMAN PREFERENCE - PRIMARY]\n{preference}\n\n[LEARNED PREFERENCES - SUPPLEMENTARY]\n{learned_preferences}"
            else:
                preference = f"[LEARNED PREFERENCES - PRIMARY]\n{learned_preferences}"
            root = MCTSNode(state=None, previous_steps=previous_steps, preference=preference, learned_preferences=learned_preferences)
            predicted_step2_seq = mcts(root, scenario_data, scenario, i, api_server="vocano", rollout_steps=rollout_steps, beam_width=3, step2_length=step2_length)
            if not predicted_step2_seq:
                print(f"Warning: No valid predicted step2 sequence for case {i}")
                continue
            # 用LLM智能对比两个step2动作序列
            prompt = prompt_dict["compare_agent_step2"]
            prompt += f"\n1.{target_step2_seq}\n2.{predicted_step2_seq}"
            response_compare = get_response_with_retry(prompt, api_server="vocano")
            print("LLM compare response:", response_compare)
            if response_compare.strip() == "1":
                correct_count += 1
            total_count += 1
            mode_str = "with_real_preference" if use_preference else "without_preference"
            print(f"Case {i} ({mode_str}): {'Correct' if response_compare.strip() == '1' else 'Incorrect'}")
            print(f"Target: {target_step2_seq}")
            print(f"Predicted: {predicted_step2_seq}")
            print(f"correct: {global_correct + correct_count}, total: {global_total + total_count}")
            # print("target_step2_seq:", target_step2_seq)
            # print("predicted_step2_seq:", predicted_step2_seq)
            # print("types:", [type(x) for x in target_step2_seq], [type(x) for x in predicted_step2_seq])
        except Exception as e:
            print(f"Error testing case {i}: {e}")
            continue
    return correct_count, total_count

def learn_and_test_pipeline(rollout_steps=3, use_preference=True):
    """完整的学习-测试流程，根据参数决定是否使用偏好"""
    total_correct = 0
    total_tests = 0
    
    # 训练场景：assembly, cooking, sorting
    train_scenarios = ["cooking", "cleaning", "sorting"]
    # 测试场景：cleaning
    test_scenario = "assembly"
    
    # 总体进度条
    total_iterations = len(user_name_list) * len(train_scenarios)
    mode_str = "with preference" if use_preference else "without preference"
    with tqdm.tqdm(total=total_iterations, desc=f"Learning and Testing Progress ({mode_str})") as pbar:
        
        for name in user_name_list:
            # 学习阶段：从训练场景学习用户偏好
            learned_preferences_all = {}
            for scenario in train_scenarios:
                pbar.set_description(f"Learning {name} - {scenario}")
                
                try:
                    print(f"\nLearning preferences for {name} in {scenario}...")
                    learned_preferences = learn_user_preferences(name, scenario, api_server="vocano")
                    learned_preferences_all[scenario] = learned_preferences
                    
                except Exception as e:
                    print(f"Error learning {name} - {scenario}: {e}")
                    learned_preferences_all[scenario] = "No specific preferences learned."
                
                pbar.update(1)
            
            # 测试阶段：在cleaning场景上预测
            pbar.set_description(f"Testing {name} - {test_scenario}")
            try:
                print(f"Testing predictions for {name} in {test_scenario} ({mode_str})...")
                # 合并所有训练场景学到的偏好
                combined_preferences = "\n\n".join([f"[{scenario} preferences]:\n{pref}" for scenario, pref in learned_preferences_all.items()])
                
                correct, total = test_with_mcts(name, test_scenario, combined_preferences, rollout_steps, use_preference=use_preference, 
                                              global_correct=total_correct, global_total=total_tests)
                
                total_correct += correct
                total_tests += total
                
                print(f"{name} - {test_scenario}: {correct}/{total} correct")
                print(f"Overall Progress: correct: {total_correct}, total: {total_tests}")
                
            except Exception as e:
                print(f"Error testing {name} - {test_scenario}: {e}")
    
    # 输出最终结果
    accuracy = total_correct / total_tests if total_tests > 0 else 0
    print(f"\nFinal Results ({mode_str}):")
    print(f"Total Correct: {total_correct}")
    print(f"Total Tests: {total_tests}")
    print(f"Overall Accuracy: {accuracy:.2%}")

def main():
    parser = argparse.ArgumentParser(description="Run LLM+MCTS for human-robot collaboration task.")
    parser.add_argument("--steps", type=int, default=3, help="Number of steps for MCTS rollout.")
    parser.add_argument("--use_preference", action="store_true", help="Whether to use preference in prediction.")
    args = parser.parse_args()

    # 运行学习-测试流程
    rollout_steps = args.steps if args.steps is not None else 3
    use_preference = args.use_preference
    
    
    print(f"Running with preference: {use_preference}")
    learn_and_test_pipeline(rollout_steps=rollout_steps, use_preference=use_preference)

if __name__ == "__main__":
    main()


