import numpy as np
import os 
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from openai import OpenAI
import openai
import tqdm
import json
from utils import check_complete_userdata, load_previous_steps, generate_task_description
from get_response import get_response
import time


# user_name_list = check_complete_userdata()
user_name_list = [ "Aiden"
]
# scenarios = ["assembly", "cleaning", "cooking", "sorting"]
scenarios = ["sorting"]
prompt_dict = json.load(open("prompts.json"))
print(user_name_list)

percent = 0.5
correct_count = 0
incorrect_count = 0
total_count = 0
for name in tqdm.tqdm(user_name_list):
    for scenario in scenarios:
        for i in range(1,11):     
            steps = load_previous_steps(name, scenario, i)                
            previous_steps = steps[:int(len(steps)*percent)]
            next_step = steps[int(len(steps)*percent)]
            scenario_data = json.load(open(f"dataset/{scenario}.json"))
            prompt = generate_task_description(scenario_data, i)
            
            if next_step["executor"] == "Human":               
                prompt += prompt_dict["base_agent"]["end_prompt_human"]
                prompt += f"previous steps: {previous_steps}\n"
                response = get_response(prompt, api_server="vocano")
            else:
                prompt += prompt_dict["base_agent"]["end_prompt_robot"]
                prompt += f"previous steps: {previous_steps}\n"
                response = get_response(prompt, api_server="vocano")
            prompt = prompt_dict["compare_agent"]["init_prompt"]
            prompt += f"1.{next_step}\n 2.{response}"
            response_compare = get_response(prompt, api_server="vocano")
            print(response_compare)
            if response_compare == "1":
                print(f"1.{next_step}\n 2.{response}")
                correct_count += 1
            else:
                incorrect_count += 1
                print(f"1.{next_step}\n 2.{response}")
            total_count += 1
            print(f"correct: {correct_count}, incorrect: {incorrect_count}, total: {total_count}")
        break
                
    

percent = 0.5
correct_count = 0
incorrect_count = 0
total_count = 0
for name in tqdm.tqdm(user_name_list):
    for scenario in scenarios:
        for i in range(1,11):     
            steps = load_previous_steps(name, scenario, i)                
            previous_steps = steps[:int(len(steps)*percent)]
            next_step = steps[int(len(steps)*percent)]
            scenario_data = json.load(open(f"dataset/{scenario}.json"))
            prompt = generate_task_description(scenario_data, i)
            prompt+="\n\n [human preference]\n"
            #read the preference /Users/<USER>/workspace/ICL_LLM/dataset/user/Aaron/assembly/preference_case1.txt
            with open(f"dataset/user/{name}/{scenario}/preference_case{i}.txt", "r") as file:
                preference = file.read()
            prompt += preference
            if next_step["executor"] == "Human":               
                prompt += prompt_dict["base_agent"]["end_prompt_human"]
                prompt += f"previous steps: {previous_steps}\n"
                response = get_response(prompt, api_server="vocano")
            else:
                prompt += prompt_dict["base_agent"]["end_prompt_robot"]
                prompt += f"previous steps: {previous_steps}\n"
                response = get_response(prompt, api_server="vocano")
            prompt = prompt_dict["compare_agent"]["init_prompt"]
            prompt += f"1.{next_step}\n 2.{response}"
            response_compare = get_response(prompt, api_server="vocano")
            print(response_compare)
            if response_compare == "1":
                print(f"1.{next_step}\n 2.{response}")
                correct_count += 1
            else:
                incorrect_count += 1
                print(f"1.{next_step}\n 2.{response}")
            total_count += 1
            print(f"correct: {correct_count}, incorrect: {incorrect_count}, total: {total_count}")
        break
                
    

for name in tqdm.tqdm(user_name_list):
    for scenario in scenarios:
        for i in range(1,11):
            with open(f"dataset/user/{name}/{scenario}/response_case{1}.txt", "r") as file:
                content = file.read()
                prompt = prompt_dict["plan_conversion_prompt"]
                prompt = prompt + content
                response = get_response(prompt, api_server="vocano")
                
                # Clean the response by removing ```json prefix if present
                cleaned_response = response
                if response.strip().startswith('```json'):
                    cleaned_response = response.split('```json')[-1].strip()
                if cleaned_response.endswith('```'):
                    cleaned_response = cleaned_response[:-3].strip()
                
                # save cleaned response to file
                with open(f"dataset/user/{name}/{scenario}/response_case{1}.json", "w") as file:
                    file.write(cleaned_response)
                print(f"save to dataset/user/{name}/{scenario}/response_case{1}.json")
            
            
            
                





import json

with open('dataset/user/Aaron/sorting/response_case3.json', 'r') as file:
    # Read all lines and join them
    content = ''.join(line.split('|')[-1] if '|' in line else line for line in file)
    # Find the JSON content between triple backticks
    json_start = content.find('[')
    json_end = content.rfind(']') + 1
    if json_start >= 0 and json_end > 0:
        json_content = content[json_start:json_end]
        steps = json.loads(json_content)
print(steps)

