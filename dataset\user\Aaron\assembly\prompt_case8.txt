---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 4 m × 6 m workshop with a long aisle in the center.
- Goal: Assemble a hydraulic press module at coordinate (2,3).

[Agents]
- Robot: Positioned at (4,6), can only move along the right boundary. Human: Positioned at (0,6), can move along the top boundary.

[Interactable Objects]
['Press Base', 'Cylinder Unit', 'Hydraulic Pump', 'Control Switch', 'Reservoir Tank', 'Pressure Gauge', 'Valve System']
[Human Preferences]
1. I prefer to approach and handle smaller and lighter objects first before moving on to larger and heavier ones, as smaller items tend to be less cumbersome.

2. I prefer to communicate clearly with the robot about which component I will handle next to avoid any collision or confusion in planning.

3. I prefer to have a clear, unobstructed path along the top boundary to ensure efficient movement back and forth.

4. I prefer to coordinate with the robot to simultaneously assemble different components when possible to save time.

5. I prefer to assemble components that are closer to my starting position initially and gradually work toward the center at (2,3) to minimize unnecessary backtracking.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

