---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 2 m × 3 m workstation with separate staging areas on the left and right.
- Goal: Assemble a basic conveyor sub-unit at coordinate (1,1).

[Agents]
- Robot: Positioned at (1,3), moves along the top row. Human: Positioned at (0,0), moves within the bottom row.

[Interactable Objects]
['Conveyor Frame', 'Roller', 'Motor Unit', 'Control Panel', 'Power Supply', 'Speed Sensor', 'Mounting Plate']
[Human Preferences]
1. I prefer to pick up the near objects first, starting with Roller #2 since it's closest to my position, to efficiently manage my movement within the workspace.

2. I prefer the robot to beep twice only when there's a significant delay or issue, rather than for minor adjustments, to avoid unnecessary interruptions.

3. I prefer to coordinate with the robot on placing the Control Panel #4 and Motor Unit #3, as they're positioned near the robot's starting area, ensuring efficient teamwork.

4. I prefer to pause for confirmation before securing items onto the Conveyor Frame #1 to ensure proper alignment with the other components.

5. I prefer to collect all components necessary for a single assembly step before proceeding, such as gathering the Mounting Plate #7 and Speed Sensor #6 simultaneously when needed for the same task.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

