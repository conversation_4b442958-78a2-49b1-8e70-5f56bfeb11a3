---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 3m × 3m sorting area with designated zones for different item categories.
- Goal: Sort and categorize items into their respective zones efficiently.

[Agents]
- Robot: Positioned at the center (1.5, 1.5) and can move freely within the area.
Human: Positioned at the entrance (0, 1.5) and can navigate the perimeter.

[Interactable Objects]
['Plastic Bottle (Recyclable)', 'Cardboard Box (Recyclable)', 'Glass Jar (Fragile)', 'Metal Can (Recyclable)', 'Paper Bag (Compostable)', 'Electronic Waste (Hazardous)', 'Aluminum Foil (Recyclable)']
[Human Preferences]
1. I prefer to begin sorting by handling the fragile item first to minimize the risk of breakage, ensuring the glass jar is placed into its correct zone with care.

2. I prefer working with the robot in a clockwise direction around the sorting area to maintain a consistent workflow and avoid crossing paths unnecessarily.

3. I prefer to handle recyclable materials in succession to build a rhythm in categorizing, starting with the cardboard box, followed by the metal can, plastic bottle, and aluminum foil.

4. I prefer to place hazardous materials into their respective zones as soon as possible to minimize the chance of accidental exposure or contamination, specifically prioritizing the electronic waste.

5. I prefer leaving compostable items such as the paper bag as the final category to handle, allowing for a cleaner workspace for any adjustments needed after sorting other items.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

