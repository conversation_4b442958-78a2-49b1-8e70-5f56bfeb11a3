1. I prefer to start by gathering smaller components, such as the Microcontroller and Sensor Array, before assembling larger parts like the Rover Chassis to prevent disorganization on the lab bench.

2. I like to take inventory of all the components in the designated assembly corner before starting the assembly to ensure nothing is misplaced.

3. I prefer to use a specific order when connecting electrical components, such as Battery Module first before driving connections, for logical assembly flow.

4. I want to consciously avoid bottlenecks in the process, by coordinating movements with the robot efficiently, such as by waiting for it to move out of my path if needed.

5. I prefer to keep the assembly area neat and organized, only taking parts needed immediately to prevent workspace clutter.