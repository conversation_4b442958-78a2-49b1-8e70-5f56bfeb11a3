---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A narrow 8m × 3m bathroom with a sink, toilet, and shower area. The floor is tile and can be slippery.
- Goal: Clean the sink, toilet, and shower area, ensuring the floor is not slippery.

[Agents]
- Human: Starts at (0,1). Robot: Starts at (7,2). The robot has a limited turn radius in tight corners.

[Interactable Objects]
['Toilet Brush', 'Bathroom Cleaner Spray', 'Microfiber Cloth', 'Trash Bag', 'Non-Slip Mat', 'Rubber Gloves', 'Floor Squeegee']
[Human Preferences]
1. I prefer to start by putting on the rubber gloves (#6) immediately to ensure hand protection throughout the cleaning process.

2. I prefer to use the non-slip mat (#5) as soon as possible to mitigate the risk of slipping on the tile floor.

3. I prefer to use the bathroom cleaner spray (#2) first for areas that need cleaning to maximize the effectiveness of the cleaner over time.

4. I prefer to collaborate with the robot to use the microfiber cloth (#3) as the final step to ensure all surfaces are streak-free.

5. I prefer to use the toilet brush (#1) last to ensure thoroughness in cleaning, following the order of spray, wipe, and scrub.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

