[{"step": 0, "executor": "Human", "action": "Move to (1,1) to collect Rover Chassis"}, {"step": 0, "executor": "Robot", "action": "Move to (4,6) (assembly corner)"}, {"step": 1, "executor": "Human", "action": "Collect <PERSON> Chassis"}, {"step": 2, "executor": "Robot", "action": "Beep to signal arrival at assembly corner"}, {"step": 2, "executor": "Human", "action": "Move to (2,2) to collect Drive Motor"}, {"step": 3, "executor": "Human", "action": "Collect Drive Motor"}, {"step": 4, "executor": "Human", "action": "Move to (3,3) to collect Battery Module"}, {"step": 5, "executor": "Human", "action": "Collect Battery Module"}, {"step": 6, "executor": "Human", "action": "Move to (4,4) to collect <PERSON>sor Array"}, {"step": 7, "executor": "Human", "action": "Collect Sensor Array"}, {"step": 8, "executor": "Human", "action": "Move to (5,5) to collect Microcontroller"}, {"step": 9, "executor": "Human", "action": "Collect Microcontroller"}, {"step": 10, "executor": "Human", "action": "Move to (6,6) to collect Wheel Set"}, {"step": 11, "executor": "Human", "action": "Collect Wheel Set"}, {"step": 12, "executor": "Human", "action": "Move to (7,7) to collect Control Display"}, {"step": 13, "executor": "Human", "action": "Collect Control Display"}, {"step": 14, "executor": "Human", "action": "Move to (4,6) to assemble rover"}, {"step": 15, "executor": "Human", "action": "Assemble Rover Chassis and Drive Motor"}, {"step": 16, "executor": "Human", "action": "Assemble Battery Module"}, {"step": 17, "executor": "Human", "action": "Assemble Sensor Array"}, {"step": 18, "executor": "Human", "action": "Assemble Microcontroller"}, {"step": 19, "executor": "Human", "action": "Assemble Wheel Set"}, {"step": 20, "executor": "Human", "action": "Assemble Control Display"}]