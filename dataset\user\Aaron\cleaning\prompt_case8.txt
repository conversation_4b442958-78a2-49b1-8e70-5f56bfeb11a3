---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 9m × 4m bedroom with a bed, wardrobe, and carpeted floor. Some delicate fabrics require gentle handling.
- Goal: Vacuum the carpet, dust surfaces, and neatly fold or hang clothes in the wardrobe.

[Agents]
- Human: Starts at (1,3). Robot: Starts at (8,0). The robot has trouble traversing thick rugs.

[Interactable Objects]
['Vacuum Cleaner', 'Dusting Cloth', 'Hanger Set', 'Fabric Freshener', 'Trash Bag', 'Lint Roller', 'Upholstery Brush']
[Human Preferences]
1. I prefer to begin by quickly acquiring the Dusting Cloth (#2) and Hanger Set (#3) since they are closest to me, ensuring I can start dusting and organizing clothes efficiently.

2. I prefer to fold or hang clothes immediately after dusting to maintain an organized space and reduce the risk of dust settling on freshly cleaned surfaces.

3. I prefer for the robot to utilize the Upholstery Brush (#7) before attempting to vacuum to ensure surface debris is loosened and can be effectively collected, minimizing the impact of the thick carpet on the vacuuming process.

4. I prefer the robot to beep softly once upon picking up any potentially delicate item before handling it, ensuring careful treatment and avoiding damage to delicate fabrics.

5. I prefer to finish cleaning tasks near the exit last, starting from the farthest point to ensure no interference with previously cleaned areas and a smooth transition out of the room upon completion.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

