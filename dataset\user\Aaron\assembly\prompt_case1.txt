---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 2 m × 2 m workstation with a small central assembly zone.
- Goal: Assemble a compact sensor unit at coordinate (1,1).

[Agents]
- Robot: Positioned at (2,2), restricted to top/right edges. Human: Positioned at (0,0), restricted to bottom/left edges.

[Interactable Objects]
['Base Frame', 'Sensor Module', 'Battery Pack', 'Mounting Bracket', 'Control Chip', 'Signal Booster', 'Status Display']
[Human Preferences]
1. I prefer to start assembling with the Base Frame #1 at (0.10, 1.83) as it serves as a foundation for the other components.

2. I prefer carrying and placing the Sensor Module #2 at (0.69, 0.49) next, minimizing travel time due to proximity to my starting position.

3. I prefer the robot to hold onto the heavier Battery Pack #3 at (0.56, 1.48) until later in the assembly process to keep it out of the immediate workspace.

4. I prefer to assemble the Signal Booster #6 at (0.38, 1.34) and Status Display #7 at (0.34, 1.22) in sequence, as they are closely positioned together, optimizing motion efficiency.

5. I prefer that the robot places the Control Chip #5 at (1.01, 1.47) last, ensuring all foundational elements are securely in place beforehand.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

