#!/usr/bin/env python3
"""
LoRA训练脚本 - 使用DeepSeek-V2作为基础模型
训练人机协作规划任务的Alpaca格式数据
"""

import os
import json
import torch
import logging
from dataclasses import dataclass, field
from typing import Optional, Dict, Sequence
import transformers
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
    BitsAndBytesConfig
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="mistralai/Mistral-7B-Instruct-v0.3")
    trust_remote_code: bool = field(default=True)
    use_flash_attention_2: bool = field(default=True)

@dataclass
class DataArguments:
    data_path: str = field(default="dataset/alpaca/all_alpaca_data.json")
    max_train_samples: Optional[int] = field(default=None)
    max_eval_samples: Optional[int] = field(default=None)

@dataclass
class CustomTrainingArguments(TrainingArguments):
    cache_dir: Optional[str] = field(default=None)
    optim: str = field(default="adamw_torch")
    model_max_length: int = field(default=4096)
    use_lora: bool = field(default=True)
    lora_r: int = field(default=16)
    lora_alpha: int = field(default=32)
    lora_dropout: float = field(default=0.1)
    lora_target_modules: str = field(default="q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj")
    use_4bit: bool = field(default=True)
    use_8bit: bool = field(default=False)

class SupervisedDataset(Dataset):
    """监督学习数据集"""
    
    def __init__(self, data_path: str, tokenizer: transformers.PreTrainedTokenizer, max_length: int = 4096):
        super(SupervisedDataset, self).__init__()
        
        logger.info(f"Loading data from {data_path}")
        with open(data_path, 'r', encoding='utf-8') as f:
            list_data_dict = json.load(f)
        
        logger.info(f"Loaded {len(list_data_dict)} examples")
        
        # 格式化数据
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.data = []
        
        for example in list_data_dict:
            # 构建对话格式
            conversation = self._format_conversation(example)
            self.data.append(conversation)
    
    def _format_conversation(self, example):
        """格式化单个对话"""
        # Mistral的对话格式使用特殊token
        instruction = example['instruction']
        input_text = example['input']
        output_text = example['output']

        # 构建Mistral格式的prompt
        if input_text.strip():
            # 使用Mistral的指令格式
            prompt = f"<s>[INST] {instruction}\n\n{input_text} [/INST] "
        else:
            prompt = f"<s>[INST] {instruction} [/INST] "

        # 完整对话
        full_text = prompt + output_text + "</s>"

        return {
            'prompt': prompt,
            'output': output_text,
            'full_text': full_text
        }
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, i):
        return self.data[i]

def preprocess_function(examples, tokenizer, max_length):
    """预处理函数"""
    model_inputs = {"input_ids": [], "attention_mask": [], "labels": []}
    
    for example in examples:
        full_text = example['full_text']
        prompt = example['prompt']
        
        # 编码完整文本
        full_encoded = tokenizer(
            full_text,
            truncation=True,
            max_length=max_length,
            padding=False,
            return_tensors=None
        )
        
        # 编码prompt部分
        prompt_encoded = tokenizer(
            prompt,
            truncation=True,
            max_length=max_length,
            padding=False,
            return_tensors=None
        )
        
        input_ids = full_encoded["input_ids"]
        attention_mask = full_encoded["attention_mask"]
        
        # 创建labels，只对输出部分计算loss
        labels = input_ids.copy()
        prompt_length = len(prompt_encoded["input_ids"])
        
        # 将prompt部分的labels设为-100（忽略）
        for i in range(prompt_length):
            if i < len(labels):
                labels[i] = -100
        
        model_inputs["input_ids"].append(input_ids)
        model_inputs["attention_mask"].append(attention_mask)
        model_inputs["labels"].append(labels)
    
    return model_inputs

def load_model_and_tokenizer(model_args, training_args):
    """加载模型和分词器"""
    
    # 配置量化
    quantization_config = None
    if training_args.use_4bit:
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
    elif training_args.use_8bit:
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    
    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(
        model_args.model_name_or_path,
        trust_remote_code=model_args.trust_remote_code,
        padding_side="right",
        model_max_length=training_args.model_max_length,
    )
    
    # 设置pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 加载模型
    model = AutoModelForCausalLM.from_pretrained(
        model_args.model_name_or_path,
        quantization_config=quantization_config,
        trust_remote_code=model_args.trust_remote_code,
        torch_dtype=torch.float16,
        device_map="auto",
        attn_implementation="flash_attention_2" if model_args.use_flash_attention_2 else None,
    )
    
    # 配置LoRA
    if training_args.use_lora:
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=training_args.lora_r,
            lora_alpha=training_args.lora_alpha,
            lora_dropout=training_args.lora_dropout,
            target_modules=training_args.lora_target_modules.split(","),
            bias="none",
        )
        
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
    
    return model, tokenizer

def main():
    # 解析参数
    parser = transformers.HfArgumentParser((ModelArguments, DataArguments, CustomTrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    
    # 设置输出目录
    if not training_args.output_dir:
        training_args.output_dir = "./output/mistral-7b-lora-human-robot-collaboration"
    
    # 创建输出目录
    os.makedirs(training_args.output_dir, exist_ok=True)
    
    # 加载模型和分词器
    logger.info("Loading model and tokenizer...")
    model, tokenizer = load_model_and_tokenizer(model_args, training_args)
    
    # 加载数据集
    logger.info("Loading dataset...")
    dataset = SupervisedDataset(
        data_path=data_args.data_path,
        tokenizer=tokenizer,
        max_length=training_args.model_max_length
    )
    
    # 分割训练集和验证集
    train_size = int(0.9 * len(dataset))
    eval_size = len(dataset) - train_size
    
    train_dataset, eval_dataset = torch.utils.data.random_split(
        dataset, [train_size, eval_size]
    )
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Eval dataset size: {len(eval_dataset)}")
    
    # 预处理数据
    def collate_fn(examples):
        return preprocess_function(examples, tokenizer, training_args.model_max_length)
    
    # 数据收集器
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=model,
        label_pad_token_id=-100,
        pad_to_multiple_of=8
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # 开始训练
    logger.info("Starting training...")
    trainer.train()
    
    # 保存模型
    logger.info("Saving model...")
    trainer.save_model()
    tokenizer.save_pretrained(training_args.output_dir)
    
    logger.info(f"Training completed! Model saved to {training_args.output_dir}")

if __name__ == "__main__":
    main()
