---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5 m × 5 m workstation with a central safety enclosure.
- Goal: Assemble a large sensor tower at coordinate (2,3).

[Agents]
- Robot: Positioned at (0,5), restricted to the top edge. Human: Positioned at (5,0), restricted to the right edge.

[Interactable Objects]
['Tower Base', 'Sensor Array', 'Battery Module', 'Control Console', 'Support Beam', 'Antenna Array', 'Power Converter']
[Human Preferences]
1. I prefer the robot to prioritize handing me items that are closest to the final assembly position at (2,3) to minimize unnecessary movement along the workstation.

2. I want the robot to remind me to check the integrity of fragile objects, like the Sensor Array #2 and <PERSON>tenna Array #6, before proceeding with the assembly.

3. I like the robot to alert me if I request an object that has already been used and assembled to avoid duplicate placements and maintain efficiency.

4. I prefer the robot to align closer to the workstation edge when passing items to me, reducing the distance and increasing the ease of handoff at the restricted edges.

5. I want the robot to suggest the most efficient pathway for me to move from my starting position at the right edge to the assembly site at (2,3), especially when carrying heavier objects.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

