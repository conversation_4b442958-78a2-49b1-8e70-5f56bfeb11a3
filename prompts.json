{"preference_generation": {"init_prompt": ""}, "case_generation": {"init_prompt": ""}, "plan_generation": {"init_prompt": ""}, "base_agent": {"end_prompt_human": "**Your Task:**\n  Based on the given previous steps,predict the next step human will take. you should return the next step in the same format as the previous steps. nothing else, do not use coordinate in the next step", "end_prompt_robot": "**Your Task:**\n  Based on the given previous steps,predict the next step robot will take. you should return the next step in the same format as the previous steps. nothing else, do not use coordinate in the next step", "executor_prediction": "**Your Task:**\n  Based on the given previous steps and task context, predict which agent (Human or Robot) should take the next step. Consider:\n1. The capabilities of each agent\n2. The current task progress\n3. Efficiency and logical sequence\n4. Human preferences if provided\n\nReturn only 'Human' or 'Robot', nothing else."}, "compare_agent": {"init_prompt": "Given two plans, determine if the actions are essentially the same in intent and main content, even if there are differences in details such as coordinates, wording, or minor descriptions. Ignore differences in positions or coordinates unless they change the core meaning of the action. If the main action and intent are the same, return 1, otherwise return 0. (your answer should be 1 or 0, nothing else)\n\nExamples:\n1.{'step': 8, 'executor': 'Robot', 'action': 'Move to central assembly zone'}\n2.{'step': 8, 'executor': 'Robot', 'action': 'Move towards central assembly zone'}\ncorrect\n1.{'step': 16, 'executor': 'Human', 'action': \"Move to collect 'Wheel Set' at (4,4)\"}\n2.{'step': 16, 'executor': 'Human', 'action': \"Move to collect 'Wheel Set'\"}\ncorrect\n1.{'step': 12, 'executor': 'Human', 'action': 'Move to Cooling Plate location'}\n2.{'step': 12, 'executor': 'Human', 'action': 'Move to Cooling Plate pickup location'}\ncorrect\n1.{'step': 9, 'executor': 'Robot', 'action': \"Pick up and place 'Sensor Module' at goal\"}\n2.{'step': 9, 'executor': 'Robot', 'action': \"Pick up 'Torque Amplifier'\"}\nincorrect\n1.{'step': 14, 'executor': 'Human', 'action': 'Move back to assembly platform'}\n2.{'step': 14, 'executor': 'Human', 'action': 'Move back to assembly platform'}\ncorrect\n Here are the two plans: \n"}, "action_evaluation": {"init_prompt": "Given a proposed action and the current task context, evaluate the quality of this action. Consider:\n1. Does it advance the task goal?\n2. Is it logical given the previous steps?\n3. Does it follow a reasonable sequence?\n4. Is it appropriate for the current executor (Human/Robot)?\n5. If human preferences are provided, does it align with those preferences?\n\nRate the action from 0 to 1, where:\n- 0: Poor action (doesn't advance task, illogical, inappropriate, or violates preferences)\n- 0.5: Average action (somewhat useful but not optimal)\n- 1: Excellent action (advances task well, logical, appropriate, and aligns with preferences)\n\nReturn only a number between 0 and 1 (e.g., 0.8, 0.3, 1.0).\n\nTask Context: "}, "scenarios": ["assembly", "cooking", "sorting", "cleaning"], "action_timings": "\n[Actions and Timings]\n- Robot Actions:\n   - move: 0.5 m/s\n   - non-moving actions: 2 second each\n- Human Actions:\n   - move: 1 m/s\n   - non-moving actions: 1 second each\n", "plan_conversion_prompt": "You are given a plan in tabular format(ignore other content) with four columns:\nTime Interval (s)\nExecutor (Human or Robot)\nAction (e.g., \"Label Rechargeable Battery\")\nPosition Change (e.g., \"(0, 2) → (0, 2)\")\nI want you to convert this plan into a JSON array where each item represents a single step.\nEach step should include:\n\"step\": the step number starting from 0\n\"executor\": the value from the Executor column\n\"action\": the value from the Action column\nIf Time Interval (s) is the same, the step number should also be the same. Arrange the step order reasonably according to the time intervals.\nDo not include the time interval or the position change.\nReturn only the JSON array (as a list of dictionaries).\nExample output:\njson\nCopy\nEdit\n[\n  {\n    \"step\": 0,\n    \"executor\": \"Human\",\n    \"action\": \"Label Rechargeable Battery (Hazardous)\"\n  },\n  {\n    \"step\": 1,\n    \"executor\": \"Human\",\n    \"action\": \"Move to Glass Fragment (Fragile)\"\n  },\n  ...\n], here is the text you need to convert: \n", "step2_prediction_prompt": "Given the previous steps (step 0 and 1) as a list of dictionaries (each with 'step', 'executor', 'action'), predict all actions for step 2. Return ONLY a Python list of dictionaries, each with keys 'step', 'executor', and 'action'. If there are multiple actions at step 2 (e.g., parallel actions), include all of them in the list. Do NOT return any explanation, code block, rationale, or extra text. The 'step' field must be 2 for all returned actions. Example input:\n[{'step': 0, 'executor': 'Human', 'action': 'Move to table'}, {'step': 1, 'executor': 'Robot', 'action': 'Pick up tool'}]\nExample output:\n[{'step': 2, 'executor': 'Human', 'action': 'Assemble part'}, {'step': 2, 'executor': 'Robot', 'action': 'Hold part'}]", "compare_agent_step2": "Given two lists of actions for step 2, determine if the actions are essentially the same in intent and main content, even if there are differences in details such as wording, order, or minor descriptions. The number of actions in each list may not be the same, but if the main actions and intent are the same, return 1, otherwise return 0. Ignore differences in positions or coordinates unless they change the core meaning of the action. Return only 1 or 0, nothing else.\n\nExamples:\n1. [{'step': 2, 'executor': 'Human', 'action': 'Assemble part'}, {'step': 2, 'executor': 'Robot', 'action': 'Hold part'}]\n2. [{'step': 2, 'executor': 'Robot', 'action': 'Hold part'}, {'step': 2, 'executor': 'Human', 'action': 'Assemble part'}]\ncorrect\n1. [{'step': 2, 'executor': 'Human', 'action': 'Move to table'}]\n2. [{'step': 2, 'executor': 'Human', 'action': 'Move to the table'}]\ncorrect\n1. [{'step': 2, 'executor': 'Human', 'action': 'Move to table'}]\n2. [{'step': 2, 'executor': 'Robot', 'action': 'Pick up tool'}]\nincorrect\nHere are the two lists:\n"}