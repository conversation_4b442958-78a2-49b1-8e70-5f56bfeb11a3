[{"step": 0, "executor": "Human", "action": "Move to Generator Housing"}, {"step": 0, "executor": "Robot", "action": "Move to Motor Unit"}, {"step": 1, "executor": "Human", "action": "Pick up Generator Housing"}, {"step": 1, "executor": "Robot", "action": "Pick up Motor Unit"}, {"step": 2, "executor": "Human", "action": "Move to assembly point"}, {"step": 2, "executor": "Robot", "action": "Move to Power Cell"}, {"step": 3, "executor": "Human", "action": "Place Generator Housing at (2,2)"}, {"step": 3, "executor": "Robot", "action": "Pick up Power Cell"}, {"step": 4, "executor": "Robot", "action": "Move to assembly point"}, {"step": 5, "executor": "Robot", "action": "Place Power Cell at (2,2)"}, {"step": 6, "executor": "Human", "action": "Move to Control Interface"}, {"step": 6, "executor": "Robot", "action": "Move to Air Filter"}, {"step": 7, "executor": "Human", "action": "Pick up Control Interface"}, {"step": 7, "executor": "Robot", "action": "Pick up Air Filter"}, {"step": 8, "executor": "Human", "action": "Move to assembly point"}, {"step": 8, "executor": "Robot", "action": "Move to Voltage Regulator"}, {"step": 9, "executor": "Human", "action": "Place Control Interface at (2,2)"}, {"step": 9, "executor": "Robot", "action": "Pick up Voltage Regulator"}, {"step": 10, "executor": "Robot", "action": "Move to assembly point"}, {"step": 11, "executor": "Robot", "action": "Place Voltage Regulator at (2,2)"}, {"step": 12, "executor": "Human", "action": "Move to Wheel Assembly"}, {"step": 12, "executor": "Robot", "action": "Move to Wheel Assembly"}, {"step": 13, "executor": "Human", "action": "Pick up Wheel Assembly"}, {"step": 13, "executor": "Robot", "action": "Pick up Wheel Assembly"}, {"step": 14, "executor": "Human", "action": "Move to assembly point"}, {"step": 14, "executor": "Robot", "action": "Move to assembly point"}, {"step": 15, "executor": "Human", "action": "Place Wheel Assembly at (2,2)"}, {"step": 15, "executor": "Robot", "action": "Place Wheel Assembly at (2,2)"}]