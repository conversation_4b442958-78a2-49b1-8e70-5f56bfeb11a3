#!/usr/bin/env python3
"""
简化的LoRA训练脚本 - 避免参数解析问题
"""

import os
import json
import torch
import logging

# 设置环境变量
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
    BitsAndBytesConfig
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupervisedDataset:
    """监督学习数据集"""

    def __init__(self, data_path: str, tokenizer, max_length: int = 2048):
        
        logger.info(f"Loading data from {data_path}")
        with open(data_path, 'r', encoding='utf-8') as f:
            list_data_dict = json.load(f)
        
        logger.info(f"Loaded {len(list_data_dict)} examples")
        
        # 格式化数据
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.data = []
        
        for example in list_data_dict:
            # 构建对话格式
            conversation = self._format_conversation(example)
            self.data.append(conversation)
    
    def _format_conversation(self, example):
        """格式化单个对话"""
        # DeepSeek的对话格式
        instruction = example['instruction']
        input_text = example['input']
        output_text = example['output']

        # 构建DeepSeek格式的prompt
        if input_text.strip():
            prompt = f"User: {instruction}\n\n{input_text}\n\nAssistant: "
        else:
            prompt = f"User: {instruction}\n\nAssistant: "

        # 完整对话
        full_text = prompt + output_text

        return {
            'prompt': prompt,
            'output': output_text,
            'full_text': full_text
        }
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, i):
        return self.data[i]

def preprocess_function(examples, tokenizer, max_length):
    """预处理函数"""
    model_inputs = {"input_ids": [], "attention_mask": [], "labels": []}
    
    for example in examples:
        full_text = example['full_text']
        prompt = example['prompt']
        
        # 编码完整文本
        full_encoded = tokenizer(
            full_text,
            truncation=True,
            max_length=max_length,
            padding=False,
            return_tensors=None
        )
        
        # 编码prompt部分
        prompt_encoded = tokenizer(
            prompt,
            truncation=True,
            max_length=max_length,
            padding=False,
            return_tensors=None
        )
        
        input_ids = full_encoded["input_ids"]
        attention_mask = full_encoded["attention_mask"]
        
        # 创建labels，只对输出部分计算loss
        labels = input_ids.copy()
        prompt_length = len(prompt_encoded["input_ids"])
        
        # 将prompt部分的labels设为-100（忽略）
        for i in range(prompt_length):
            if i < len(labels):
                labels[i] = -100
        
        model_inputs["input_ids"].append(input_ids)
        model_inputs["attention_mask"].append(attention_mask)
        model_inputs["labels"].append(labels)
    
    return model_inputs

def load_model_and_tokenizer():
    """加载模型和分词器"""
    # 使用DeepSeek 6.7B模型 - 接近7B参数量
    model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
    
    # 配置4bit量化
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4"
    )
    
    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        padding_side="right",
    )
    
    # 设置pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 加载模型
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        device_map="auto",
        low_cpu_mem_usage=True,  # 减少CPU内存使用
    )
    
    # 配置LoRA
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        bias="none",
    )

    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    # 确保模型在训练模式
    model.train()

    # 启用LoRA参数的梯度
    for name, param in model.named_parameters():
        if "lora" in name:
            param.requires_grad = True
    
    return model, tokenizer

def main():
    """主函数"""
    logger.info("开始训练...")
    
    # 设置输出目录
    output_dir = "./output/deepseek-6.7b-lora-human-robot-collaboration"
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载模型和分词器
    logger.info("Loading model and tokenizer...")
    model, tokenizer = load_model_and_tokenizer()
    
    # 加载数据集
    logger.info("Loading dataset...")
    dataset = SupervisedDataset(
        data_path="dataset/alpaca/all_alpaca_data.json",
        tokenizer=tokenizer,
        max_length=2048
    )
    
    # 分割训练集和验证集
    train_size = int(0.9 * len(dataset))
    eval_size = len(dataset) - train_size
    
    train_dataset, eval_dataset = torch.utils.data.random_split(
        dataset, [train_size, eval_size]
    )
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Eval dataset size: {len(eval_dataset)}")
    
    # 预处理数据集
    def preprocess_dataset(examples):
        model_inputs = {"input_ids": [], "attention_mask": [], "labels": []}

        for example in examples:
            full_text = example['full_text']
            prompt = example['prompt']

            # 编码完整文本
            full_encoded = tokenizer(
                full_text,
                truncation=True,
                max_length=2048,
                padding=False,
                return_tensors=None
            )

            # 编码prompt部分
            prompt_encoded = tokenizer(
                prompt,
                truncation=True,
                max_length=2048,
                padding=False,
                return_tensors=None
            )

            input_ids = full_encoded["input_ids"]
            attention_mask = full_encoded["attention_mask"]

            # 创建labels，只对输出部分计算loss
            labels = input_ids.copy()
            prompt_length = len(prompt_encoded["input_ids"])

            # 将prompt部分的labels设为-100（忽略）
            for i in range(prompt_length):
                if i < len(labels):
                    labels[i] = -100

            model_inputs["input_ids"].append(input_ids)
            model_inputs["attention_mask"].append(attention_mask)
            model_inputs["labels"].append(labels)

        return model_inputs

    # 转换数据集
    processed_train_data = preprocess_dataset(train_dataset)
    processed_eval_data = preprocess_dataset(eval_dataset)

    # 创建HuggingFace Dataset
    from datasets import Dataset as HFDataset
    train_hf_dataset = HFDataset.from_dict(processed_train_data)
    eval_hf_dataset = HFDataset.from_dict(processed_eval_data)

    # 数据收集器
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=model,
        label_pad_token_id=-100,
        pad_to_multiple_of=8
    )
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_dir,
        overwrite_output_dir=True,
        do_train=True,
        do_eval=True,
        eval_strategy="steps",
        eval_steps=1000,
        save_strategy="steps",
        save_steps=1000,
        save_total_limit=2,
        num_train_epochs=3,
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=16,
        learning_rate=2e-4,
        weight_decay=0.01,
        warmup_ratio=0.03,
        lr_scheduler_type="cosine",
        logging_steps=20,
        dataloader_num_workers=2,
        remove_unused_columns=False,
        report_to="tensorboard",
        fp16=True,
        gradient_checkpointing=True,
        optim="adamw_torch",
        seed=42,
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_hf_dataset,
        eval_dataset=eval_hf_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # 开始训练
    logger.info("Starting training...")
    trainer.train()
    
    # 保存模型
    logger.info("Saving model...")
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    logger.info(f"Training completed! Model saved to {output_dir}")

if __name__ == "__main__":
    main()
