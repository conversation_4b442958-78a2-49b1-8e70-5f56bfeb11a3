---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 7m × 7m industrial floor with multiple exit points and overhead crane access for heavy objects.
- Goal: Sort large industrial components from smaller recyclable items, ensuring hazardous waste is secured.

[Agents]
- Robot: Deployed at (3.5, 3.5) for full reach.
Human: Roves around the perimeter for safety checks and manual lifts.

[Interactable Objects]
['Steel Pipe (Bulky)', 'Small Metal Scrap (Recyclable)', 'Plastic Container (Recyclable)', 'Old Rag (Compostable)', 'Broken Glass Tube (Fragile)', 'Used Oil Filter (Hazardous)', '<PERSON><PERSON>t (Bulky)']
[Human Preferences]
1. I prefer to prioritize securing the hazardous waste (Used Oil Filter) first to minimize risk in the workspace.

2. I prefer the robot to handle the fragile item (Broken Glass Tube) with additional caution and alert me if it encounters issues.

3. I prefer that we first cluster similar recyclable items (Small Metal Scrap and Plastic Container) together before transferring them to designated areas.

4. I prefer to position bulky items (Steel Pipe and Wooden Pallet) near the available exit points for easier access when moving them out.

5. I prefer periodic check-ins with the robot every set interval (e.g., 5 minutes) to ensure that both the robot’s and my tasks are aligned and progressing as planned.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

