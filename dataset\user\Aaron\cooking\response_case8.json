[{"step": 0, "executor": "Human", "action": "Move to fridge (3,5)"}, {"step": 0, "executor": "Robot", "action": "Move to stove-side counter (1,1.5)"}, {"step": 1, "executor": "Human", "action": "Pick up <PERSON><PERSON> from fridge"}, {"step": 1, "executor": "Robot", "action": "Pick up Stock Pot from stove-side counter"}, {"step": 2, "executor": "Human", "action": "Move to stove-side counter (1,1.5)"}, {"step": 2, "executor": "Robot", "action": "Move to fridge (3,5)"}, {"step": 3, "executor": "Human", "action": "Place Broth Carton on stove-side counter"}, {"step": 3, "executor": "Robot", "action": "Pick up Onion from fridge"}, {"step": 4, "executor": "Human", "action": "Move to fridge (3,5)"}, {"step": 4, "executor": "Robot", "action": "Move to stove-side counter (1,1.5)"}, {"step": 5, "executor": "Human", "action": "Pick up Celery from fridge"}, {"step": 5, "executor": "Robot", "action": "Place Onion on stove-side counter"}, {"step": 6, "executor": "Human", "action": "Move to stove-side counter (1,1.5)"}, {"step": 6, "executor": "Robot", "action": "Move to fridge (3,5)"}, {"step": 7, "executor": "Human", "action": "Place Celery on stove-side counter"}, {"step": 7, "executor": "Robot", "action": "Pick up Salt Shaker from fridge"}, {"step": 8, "executor": "Human", "action": "Move to fridge (3,5)"}, {"step": 8, "executor": "Robot", "action": "Move to stove-side counter (1,1.5)"}, {"step": 9, "executor": "Human", "action": "Pick up Pepper Grinder from fridge"}, {"step": 9, "executor": "Robot", "action": "Place Salt Shaker on stove-side counter"}, {"step": 10, "executor": "Human", "action": "Move to stove-side counter (1,1.5)"}, {"step": 10, "executor": "Robot", "action": "Move to fridge (3,5)"}, {"step": 11, "executor": "Human", "action": "Place Pepper Grinder on stove-side counter"}, {"step": 11, "executor": "Robot", "action": "Pick up Soup Spoon from fridge"}, {"step": 12, "executor": "Robot", "action": "Move to stove-side counter (1,1.5)"}, {"step": 13, "executor": "Robot", "action": "Place Soup Spoon on stove-side counter"}]