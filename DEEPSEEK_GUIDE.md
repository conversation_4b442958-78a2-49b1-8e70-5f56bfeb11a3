# DeepSeek 6.7B LoRA训练指南

## 🤖 关于DeepSeek 6.7B模型

### 模型选择说明

我们选择 `deepseek-ai/deepseek-coder-6.7b-instruct` 作为训练基础模型，原因如下：

1. **参数量适中**: 6.7B参数，接近您要求的7B规模
2. **开放访问**: 无需HuggingFace认证，可直接下载使用
3. **指令微调**: 已经过指令微调，适合对话任务
4. **硬件友好**: 在6GB显存上可以正常运行（使用4bit量化）
5. **中文支持**: DeepSeek对中文有很好的支持

### 可用的DeepSeek模型对比

| 模型 | 参数量 | 大小 | 特点 | 推荐度 |
|------|--------|------|------|--------|
| deepseek-coder-1.3b-instruct | 1.3B | ~3GB | 最小，速度快 | ⭐⭐⭐ |
| **deepseek-coder-6.7b-instruct** | **6.7B** | **~13GB** | **平衡性能和资源** | **⭐⭐⭐⭐⭐** |
| deepseek-llm-7b-chat | 7B | ~14GB | 通用对话模型 | ⭐⭐⭐⭐ |
| DeepSeek-V2 | 236B | ~500GB | 最强性能，资源需求极高 | ⭐⭐ |

## 🚀 使用方法

### 直接运行训练
```bash
# 使用简化脚本（推荐）
python train_simple.py

# 或使用6GB优化脚本
python start_training_6gb.py
```

### 手动指定参数
```bash
python train_lora.py \
    --model_name_or_path deepseek-ai/deepseek-coder-6.7b-instruct \
    --data_path dataset/alpaca/all_alpaca_data.json \
    --output_dir ./output/deepseek-6.7b-lora-human-robot-collaboration \
    --use_4bit \
    --gradient_checkpointing
```

## 💾 硬件要求

### DeepSeek 6.7B + LoRA + 4bit量化

- **最低配置**: 6GB GPU显存 (RTX 3060)
- **推荐配置**: 8GB GPU显存 (RTX 4060 Ti)
- **内存**: 16GB RAM
- **存储**: 20GB (模型13GB + 训练数据7GB)

### 训练参数优化

```python
# 6GB显存优化设置
training_args = TrainingArguments(
    per_device_train_batch_size=1,      # 最小批次
    gradient_accumulation_steps=16,     # 补偿小批次
    model_max_length=2048,              # 减少序列长度
    fp16=True,                          # 半精度
    gradient_checkpointing=True,        # 梯度检查点
    use_4bit=True,                      # 4bit量化
)
```

## 🔧 DeepSeek特殊配置

### 对话格式
DeepSeek使用标准的对话格式：
```
User: 用户指令

用户输入内容
