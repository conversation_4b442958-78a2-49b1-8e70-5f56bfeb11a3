[{"step": 0, "executor": "Robot", "action": "Pick up Antibacterial Wipes #1"}, {"step": 0, "executor": "Human", "action": "Move to desk (1m)"}, {"step": 1, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 2, "executor": "Robot", "action": "Hand over Antibacterial Wipes #1 to human"}, {"step": 3, "executor": "Human", "action": "Use Antibacterial Wipes to clean desk"}, {"step": 3, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 4, "executor": "Robot", "action": "Pick up Desktop Organizer #5"}, {"step": 5, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 6, "executor": "Robot", "action": "Hand over Desktop Organizer #5 to human"}, {"step": 7, "executor": "Human", "action": "Place Desktop Organizer on desk"}, {"step": 7, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 8, "executor": "Robot", "action": "Pick up Furniture Polish #7"}, {"step": 9, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 10, "executor": "Robot", "action": "Hand over Furniture Polish #7 to human"}, {"step": 11, "executor": "Human", "action": "Use Furniture Polish on desk and chair"}, {"step": 12, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 13, "executor": "Robot", "action": "Pick up Paper Towels #2"}, {"step": 14, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 15, "executor": "Robot", "action": "Hand over Paper Towels #2 to human"}, {"step": 16, "executor": "Human", "action": "Use Paper Towels to ensure papers remain dry"}, {"step": 17, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 18, "executor": "Robot", "action": "Pick up Compressed Air #3"}, {"step": 19, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 20, "executor": "Robot", "action": "Hand over Compressed Air #3 to human"}, {"step": 21, "executor": "Human", "action": "Use Compressed Air to clean keyboard"}, {"step": 22, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 23, "executor": "Robot", "action": "Pick up Keyboard Cleaner <PERSON><PERSON> #6"}, {"step": 24, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 25, "executor": "Robot", "action": "Hand over Keyboard Cleaner <PERSON><PERSON> #6 to human"}, {"step": 26, "executor": "Human", "action": "Use Keyboard Clean<PERSON> <PERSON><PERSON> on keyboard"}, {"step": 27, "executor": "Robot", "action": "Move to shelves (2m, 4s)"}, {"step": 28, "executor": "Robot", "action": "Pick up Trash Bin #4"}, {"step": 29, "executor": "Robot", "action": "Move to human at desk (2m, 4s)"}, {"step": 30, "executor": "Robot", "action": "Hand over Trash Bin #4 to human"}, {"step": 31, "executor": "Human", "action": "Place Trash Bin on desk"}]