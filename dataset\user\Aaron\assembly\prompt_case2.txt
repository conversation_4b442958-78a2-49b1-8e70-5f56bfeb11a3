---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 3 m × 3 m workstation with labeled assembly corners.
- Goal: Assemble a small robotic arm at coordinate (1,2).

[Agents]
- Robot: Positioned at (3,0), can move along the perimeter. Human: Positioned at (0,3), can move only along diagonal walkways.

[Interactable Objects]
['Arm Base', 'Joint Motor', 'Sensor Module', 'Control Circuit', 'Mini Battery', 'Torque Amplifier', 'Rotation Sensor']
[Human Preferences]
1. I prefer to handle small and delicate components like the Sensor Module #3 and Mini Battery #5, leaving the robot to manage heavier or bulkier items to ensure precision and care in assembly.

2. I prefer for us to coordinate when retrieving items near each other, like Arm Base #1 and Rotation Sensor #7, to avoid unnecessary collisions or delays when both agents are in proximity.

3. I prefer to start the assembly process by working on components that are closest to my initial position, such as the Control Circuit #4, to make use of my faster movement speed efficiently.

4. I prefer the robot to assist in holding or stabilizing components at the assembly corner (1,2) as I install them, ensuring alignment and safety during the assembly process. 

5. I prefer to take a short break after the completion of a significant sub-assembly to verify the integrity and correctness of the completed steps before moving on to the next stage.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

