1. I prefer to move to interactable objects that are farthest from my initial position first to reduce backtracking and save time.  
2. I want the robot to notify me when it is about to enter or leave my quadrant for any handover, ensuring clear communication and coordination.  
3. I would like to keep the workspace organized by assembling components in a specific order: starting with items that involve intricate assembly, like the Power Core and Control Board, to minimize handling errors.  
4. I prefer that we prioritize assembling items that are closest to their respective corner work areas to maintain workflow efficiency and minimize movement.  
5. I enjoy keeping a sequential checklist of completed tasks and components as I move through the assembly process, allowing for a clear sense of progress and task tracking.  