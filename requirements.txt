# LoRA训练所需的依赖包

# 核心深度学习框架
torch>=2.0.0
torchvision
torchaudio

# Transformers生态
transformers>=4.36.0
datasets>=2.14.0
tokenizers>=0.15.0
accelerate>=0.24.0

# LoRA和量化
peft>=0.7.0
bitsandbytes>=0.41.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0

# 训练监控和可视化
tensorboard>=2.14.0
wandb  # 可选，用于实验跟踪

# 其他工具
tqdm>=4.65.0
scipy>=1.10.0
scikit-learn>=1.3.0

# YAML配置文件支持
PyYAML>=6.0

# Flash Attention (可选，需要CUDA)
# flash-attn>=2.3.0  # 取消注释以启用，需要编译

# 开发和调试工具
ipython
jupyter
matplotlib
seaborn
