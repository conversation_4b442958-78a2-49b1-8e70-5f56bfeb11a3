---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A large commercial 10m × 6m catering kitchen with multiple prep stations. Walk-in freezer at (8,1) and dry storage at (9.5,5.5).
- Goal: Collect frozen items from the freezer and dry items from storage, then gather everything at the main prep station at (4,3).

[Agents]
- Human: Starts at (2,3). Robot: Starts at (7,4). Robot must operate in designated aisles to avoid staff.

[Interactable Objects]
['Frozen Chicken Box', 'Frozen Vegetables Bag', 'Rice Bag', 'Spice Container', 'Large Mixing Bowl', 'Cooking Oil Jug', 'Aluminum Foil Roll']
[Human Preferences]
1. I prefer to collect items in clusters based on their location to minimize movement, starting with items in the freezer and then proceed to the storage.

2. I prefer to handle the heaviest items first for better organization and space management at the prep station.

3. I prefer the robot to alert me when it is approaching or passing by me, ensuring safe and coordinated movement in the kitchen.

4. I prefer placing perishable frozen items closer to the freezer's entrance when gathering them, reducing the time they stay out of a cold environment.

5. I prefer the robot to monitor and indicate when all required items have been gathered at the prep station before ending the task.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

