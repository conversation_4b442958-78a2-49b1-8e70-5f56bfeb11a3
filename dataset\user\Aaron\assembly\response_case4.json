[{"step": 0, "executor": "Human", "action": "Move to Roller #2"}, {"step": 0, "executor": "Robot", "action": "Move to Control Panel #4"}, {"step": 1, "executor": "Human", "action": "Pick up Roller #2"}, {"step": 1, "executor": "Robot", "action": "Pick up Control Panel #4"}, {"step": 2, "executor": "Human", "action": "Move to Mounting Plate #7"}, {"step": 2, "executor": "Robot", "action": "Move to Motor Unit #3"}, {"step": 3, "executor": "Human", "action": "Pick up Mounting Plate #7"}, {"step": 3, "executor": "Robot", "action": "Pick up Motor Unit #3"}, {"step": 4, "executor": "Human", "action": "Move to Speed Sensor #6"}, {"step": 4, "executor": "Robot", "action": "Move to Conveyor Frame #1"}, {"step": 5, "executor": "Human", "action": "Pick up <PERSON> Sensor #6"}, {"step": 5, "executor": "Robot", "action": "Place Control Panel #4 on Conveyor Frame #1"}, {"step": 6, "executor": "Human", "action": "Move to Conveyor Frame #1"}, {"step": 6, "executor": "Robot", "action": "Place Motor Unit #3 on Conveyor Frame #1"}, {"step": 7, "executor": "Human", "action": "Place Roller #2 on Conveyor Frame #1"}, {"step": 8, "executor": "Human", "action": "Place Mounting Plate #7 on Conveyor Frame #1"}, {"step": 9, "executor": "Human", "action": "Place Speed Sensor #6 on Conveyor Frame #1"}, {"step": 10, "executor": "Human", "action": "Pause for confirmation before securing items"}, {"step": 11, "executor": "Human", "action": "Secure all items on Conveyor Frame #1"}]