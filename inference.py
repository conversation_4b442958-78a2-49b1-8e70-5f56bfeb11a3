#!/usr/bin/env python3
"""
推理脚本 - 使用训练好的LoRA模型进行推理
"""

import torch
import json
import argparse
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HumanRobotCollaborationInference:
    """人机协作规划推理类"""
    
    def __init__(self, base_model_path, lora_model_path, device="auto"):
        self.device = device
        self.load_model(base_model_path, lora_model_path)
    
    def load_model(self, base_model_path, lora_model_path):
        """加载模型和分词器"""
        logger.info("Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            base_model_path,
            trust_remote_code=True,
            padding_side="left"
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        logger.info("Loading base model...")
        self.model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map=self.device,
            trust_remote_code=True
        )
        
        logger.info("Loading LoRA weights...")
        self.model = PeftModel.from_pretrained(
            self.model,
            lora_model_path,
            torch_dtype=torch.float16
        )
        
        self.model.eval()
        logger.info("Model loaded successfully!")
    
    def format_prompt(self, instruction, input_text=""):
        """格式化输入prompt - Mistral格式"""
        if input_text.strip():
            prompt = f"<s>[INST] {instruction}\n\n{input_text} [/INST] "
        else:
            prompt = f"<s>[INST] {instruction} [/INST] "
        return prompt
    
    def generate_response(self, prompt, max_length=2048, temperature=0.7, top_p=0.9, do_sample=True):
        """生成回复"""
        inputs = self.tokenizer(prompt, return_tensors="pt", padding=True)
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_length=max_length,
                temperature=temperature,
                top_p=top_p,
                do_sample=do_sample,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
        
        # 解码输出
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取回复部分 - Mistral格式
        if "[/INST] " in response:
            response = response.split("[/INST] ", 1)[1]

        # 移除结束标记
        if "</s>" in response:
            response = response.split("</s>", 1)[0]
        
        return response.strip()
    
    def plan_collaboration(self, scenario_description, human_preferences="", **kwargs):
        """规划人机协作任务"""
        instruction = "Generate an optimal human-robot collaboration plan based on the given scenario, constraints, and human preferences."
        
        input_text = scenario_description
        if human_preferences:
            input_text += f"\n\n[Human Preferences]\n{human_preferences}"
        
        prompt = self.format_prompt(instruction, input_text)
        response = self.generate_response(prompt, **kwargs)
        
        return response

def load_test_cases():
    """加载测试用例"""
    test_cases = [
        {
            "name": "Assembly Task",
            "scenario": """---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time.

[Scenario]
- A 2 m × 2 m workstation with a small central assembly zone.
- Goal: Assemble a compact sensor unit at coordinate (1,1).

[Agents]
- Robot: Positioned at (2,2), restricted to top/right edges. Human: Positioned at (0,0), restricted to bottom/left edges.

[Interactable Objects]
['Base Frame', 'Sensor Module', 'Battery Pack', 'Mounting Bracket', 'Control Chip', 'Signal Booster', 'Status Display']

[Actions and Timings]
- Robot Actions: move: 0.5 m/s, non-moving actions: 2 second each
- Human Actions: move: 1 m/s, non-moving actions: 1 second each""",
            "preferences": """1. I prefer to prioritize retrieving objects that are closer to my current position on the bottom/left edges to minimize my movement.
2. I want to assemble the sensor unit with objects that require minimal obstruction from the robot's zone.
3. I prefer that the robot maintains a safe distance when I am handling objects.
4. I prefer to have a clear verbal acknowledgment from the robot before it moves towards the central assembly zone.
5. I want to assemble components in an order that balances my and the robot's actions."""
        },
        {
            "name": "Cleaning Task", 
            "scenario": """[Scenario]
- A 3 m × 3 m kitchen area with multiple cleaning zones.
- Goal: Clean and organize the kitchen workspace.

[Agents]
- Robot: Mobile cleaning robot with vacuum and mopping capabilities.
- Human: Can handle delicate items and detailed cleaning tasks.

[Interactable Objects]
['Dishes', 'Utensils', 'Countertop', 'Floor', 'Appliances', 'Trash', 'Cleaning Supplies']""",
            "preferences": """1. I prefer to handle fragile dishes and glassware myself.
2. I want the robot to focus on floor cleaning while I handle countertops.
3. I prefer to coordinate timing so we don't interfere with each other."""
        }
    ]
    
    return test_cases

def main():
    parser = argparse.ArgumentParser(description="Human-Robot Collaboration Planning Inference")
    parser.add_argument("--base_model", default="mistralai/Mistral-7B-Instruct-v0.3", help="Base model path")
    parser.add_argument("--lora_model", default="./output/mistral-7b-lora-human-robot-collaboration", help="LoRA model path")
    parser.add_argument("--interactive", action="store_true", help="Interactive mode")
    parser.add_argument("--test_cases", action="store_true", help="Run test cases")
    parser.add_argument("--max_length", type=int, default=2048, help="Max generation length")
    parser.add_argument("--temperature", type=float, default=0.7, help="Generation temperature")
    
    args = parser.parse_args()
    
    # 初始化推理器
    print("Initializing inference engine...")
    inferencer = HumanRobotCollaborationInference(
        base_model_path=args.base_model,
        lora_model_path=args.lora_model
    )
    
    if args.test_cases:
        # 运行测试用例
        print("\n=== Running Test Cases ===")
        test_cases = load_test_cases()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test Case {i}: {test_case['name']} ---")
            print(f"Scenario: {test_case['scenario'][:100]}...")
            
            response = inferencer.plan_collaboration(
                scenario_description=test_case['scenario'],
                human_preferences=test_case['preferences'],
                max_length=args.max_length,
                temperature=args.temperature
            )
            
            print(f"\nGenerated Plan:\n{response}")
            print("-" * 80)
    
    if args.interactive:
        # 交互模式
        print("\n=== Interactive Mode ===")
        print("Enter scenario descriptions and human preferences to generate collaboration plans.")
        print("Type 'quit' to exit.")
        
        while True:
            print("\n" + "="*50)
            scenario = input("Enter scenario description: ").strip()
            
            if scenario.lower() == 'quit':
                break
            
            preferences = input("Enter human preferences (optional): ").strip()
            
            print("\nGenerating collaboration plan...")
            response = inferencer.plan_collaboration(
                scenario_description=scenario,
                human_preferences=preferences,
                max_length=args.max_length,
                temperature=args.temperature
            )
            
            print(f"\nGenerated Plan:\n{response}")
    
    if not args.test_cases and not args.interactive:
        print("Please specify --test_cases or --interactive mode")

if __name__ == "__main__":
    main()
