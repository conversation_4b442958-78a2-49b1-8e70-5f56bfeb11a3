[{"step": 0, "executor": "Human", "action": "Move to (1,0)"}, {"step": 0, "executor": "Robot", "action": "Move to (2,5)"}, {"step": 1, "executor": "Human", "action": "Pick up Generator Housing at (1,0)"}, {"step": 1, "executor": "Robot", "action": "Announce handing Motor Unit"}, {"step": 2, "executor": "Robot", "action": "Hand Motor Unit to Human"}, {"step": 3, "executor": "Human", "action": "Move to (2,0)"}, {"step": 3, "executor": "Robot", "action": "Move to (1,5)"}, {"step": 4, "executor": "Human", "action": "Pick up Control Interface at (2,0)"}, {"step": 4, "executor": "Robot", "action": "Announce handing Air Filter"}, {"step": 5, "executor": "Robot", "action": "Hand Air Filter to Human"}, {"step": 6, "executor": "Human", "action": "Move to (3,0)"}, {"step": 6, "executor": "Robot", "action": "Move to (3,5)"}, {"step": 7, "executor": "Human", "action": "Pick up Voltage Regulator at (3,0)"}, {"step": 7, "executor": "Robot", "action": "Announce handing Wheel Assembly"}, {"step": 8, "executor": "Robot", "action": "Hand Wheel Assembly to Human"}, {"step": 9, "executor": "Human", "action": "Move to (2,2)"}, {"step": 10, "executor": "Human", "action": "Assemble components at (2,2)"}]