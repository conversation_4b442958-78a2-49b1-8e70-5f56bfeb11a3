[{"step": 0, "executor": "Human", "action": "Pick up <PERSON><PERSON> (#2)"}, {"step": 0, "executor": "Robot", "action": "Move to Hanger Set (#3)"}, {"step": 1, "executor": "Human", "action": "Move to wardrobe for dusting"}, {"step": 2, "executor": "Robot", "action": "Pick up <PERSON><PERSON> (#3)"}, {"step": 2, "executor": "Human", "action": "Dust wardrobe surfaces"}, {"step": 3, "executor": "Robot", "action": "Move to human for Hanger Set delivery"}, {"step": 3, "executor": "Human", "action": "Move to bed for dusting"}, {"step": 4, "executor": "Robot", "action": "Hand over <PERSON><PERSON> (#3) to human"}, {"step": 4, "executor": "Human", "action": "Dust bed surfaces"}, {"step": 5, "executor": "Robot", "action": "Move to Fabric Freshener (#4)"}, {"step": 6, "executor": "Human", "action": "Move to wardrobe for organizing clothes"}, {"step": 6, "executor": "Robot", "action": "Pick up <PERSON><PERSON><PERSON> (#4)"}, {"step": 7, "executor": "Human", "action": "Organize clothes in wardrobe"}, {"step": 8, "executor": "Robot", "action": "Move to human for Fabric Freshener delivery"}, {"step": 8, "executor": "Human", "action": "Use Lint Roller (#6) on delicate fabrics"}, {"step": 9, "executor": "Robot", "action": "Hand over Fab<PERSON> Freshener (#4) to human"}, {"step": 10, "executor": "Human", "action": "Apply <PERSON><PERSON><PERSON>ener (#4) to clothes"}, {"step": 10, "executor": "Robot", "action": "Move to Vacuum Cleaner (#1)"}, {"step": 11, "executor": "Human", "action": "Move to Vacuum Cleaner (#1)"}, {"step": 12, "executor": "Robot", "action": "Pick up <PERSON><PERSON><PERSON><PERSON> Cleaner (#1)"}, {"step": 12, "executor": "Human", "action": "Prepare to vacuum carpet"}, {"step": 13, "executor": "Robot", "action": "Hand over Vacuum Cleaner (#1) to human"}, {"step": 14, "executor": "Human", "action": "Vacuum carpet"}, {"step": 14, "executor": "Robot", "action": "Move to Trash Bag (#5)"}, {"step": 15, "executor": "Robot", "action": "Pick up Trash Bag (#5)"}, {"step": 16, "executor": "Robot", "action": "Move to human for Trash Bag delivery"}, {"step": 17, "executor": "Robot", "action": "Hand over Trash Bag (#5) to human"}, {"step": 18, "executor": "Human", "action": "Dispose of trash using Trash Bag (#5)"}]