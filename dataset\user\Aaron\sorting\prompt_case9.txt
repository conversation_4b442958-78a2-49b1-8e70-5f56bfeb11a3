---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 4m × 6m shipping dock with marked sections for fragile, chemical, and recyclable items.
- Goal: Prepare parcels by separating fragile and hazardous items, then label recyclable items clearly.

[Agents]
- Robot: Moves on a fixed path along x=2.
Human: Freely moves within remaining space for final checks.

[Interactable Objects]
['Bubble-Wrapped Glass (Fragile)', 'Plastic Wrap (Non-Recyclable)', 'Aluminum Can (Recyclable)', 'Paper Box (Recyclable)', 'Wooden Block (Bulky)', 'Disposable Battery (Hazardous)', 'Waxed Cardboard (Non-Recyclable)']
[Human Preferences]
1. I prefer the human to start by reviewing fragile and hazardous items first to ensure they are handled with caution, reducing the risk of accidents with the Bubble-Wrapped Glass #1 and Disposable Battery #6.

2. I want the human to verify the labels on all recyclable items like the Aluminum Can #3 and Paper Box #4 before they are moved to ensure the correct labeling and placement.

3. I prefer the robot to notify the human each time it crosses the fragile section coordinates to prevent the human from getting in its path and to avoid any accidental bumping.

4. I want the human to check the positions of bulky and non-recyclable items, such as the Wooden Block #5 and Waxed Cardboard #7, to avoid congestion near the recycle marking zone.

5. I prefer any interaction with the robot to be clear and concise, with the robot providing a summary of completed tasks after each section is processed, ensuring a smooth workflow and minimizing confusion.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

