#!/usr/bin/env python3
"""
HuggingFace登录设置脚本
"""

import os
import sys
from huggingface_hub import login, whoami
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_login_status():
    """检查当前登录状态"""
    try:
        user_info = whoami()
        logger.info(f"✓ 已登录HuggingFace，用户: {user_info['name']}")
        return True
    except Exception:
        logger.info("❌ 未登录HuggingFace")
        return False

def login_to_hf():
    """登录到HuggingFace"""
    print("=== HuggingFace登录设置 ===")
    print("要访问Mistral模型，需要HuggingFace账号和token")
    print()
    
    # 检查当前状态
    if check_login_status():
        choice = input("已经登录，是否重新登录? (y/n): ").lower().strip()
        if choice not in ['y', 'yes']:
            return True
    
    print("\n获取HuggingFace Token的步骤:")
    print("1. 访问 https://huggingface.co/settings/tokens")
    print("2. 创建一个新的token (选择 'Read' 权限)")
    print("3. 复制token")
    print()
    
    # 方式1: 交互式登录
    print("方式1: 交互式登录")
    try:
        login()
        logger.info("✓ 登录成功!")
        return True
    except Exception as e:
        logger.error(f"交互式登录失败: {e}")
    
    # 方式2: 手动输入token
    print("\n方式2: 手动输入token")
    token = input("请输入您的HuggingFace token: ").strip()
    
    if not token:
        logger.error("未输入token")
        return False
    
    try:
        login(token=token)
        logger.info("✓ 登录成功!")
        return True
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return False

def test_model_access():
    """测试模型访问权限"""
    logger.info("测试模型访问权限...")
    
    models_to_test = [
        "mistralai/Mistral-7B-Instruct-v0.3",
        "mistralai/Mistral-7B-v0.1",
        "microsoft/DialoGPT-medium"
    ]
    
    accessible_models = []
    
    for model_name in models_to_test:
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
            logger.info(f"✓ 可访问: {model_name}")
            accessible_models.append(model_name)
        except Exception as e:
            logger.warning(f"❌ 无法访问: {model_name} - {str(e)[:100]}...")
    
    return accessible_models

def recommend_alternative_models():
    """推荐替代模型"""
    print("\n=== 替代模型推荐 ===")
    
    alternatives = [
        {
            "name": "mistralai/Mistral-7B-v0.1",
            "description": "Mistral 7B基础版本，无需认证",
            "pros": "开源，性能好，7B参数",
            "cons": "需要更多指令微调"
        },
        {
            "name": "microsoft/DialoGPT-medium", 
            "description": "微软对话模型，无需认证",
            "pros": "专门用于对话，无需认证",
            "cons": "参数较少(345M)，性能一般"
        },
        {
            "name": "google/flan-t5-large",
            "description": "Google T5模型，无需认证", 
            "pros": "指令微调，性能稳定",
            "cons": "不是纯生成模型"
        }
    ]
    
    for i, model in enumerate(alternatives, 1):
        print(f"{i}. {model['name']}")
        print(f"   描述: {model['description']}")
        print(f"   优点: {model['pros']}")
        print(f"   缺点: {model['cons']}")
        print()

def update_training_scripts(model_name):
    """更新训练脚本中的模型名称"""
    files_to_update = [
        "train_simple.py",
        "train_lora.py", 
        "start_training_6gb.py"
    ]
    
    logger.info(f"更新训练脚本使用模型: {model_name}")
    
    for filename in files_to_update:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换模型名称
                old_patterns = [
                    'mistralai/Mistral-7B-Instruct-v0.3',
                    'mistralai/Mistral-7B-v0.1'
                ]
                
                for pattern in old_patterns:
                    content = content.replace(f'"{pattern}"', f'"{model_name}"')
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✓ 已更新: {filename}")
                
            except Exception as e:
                logger.warning(f"更新 {filename} 失败: {e}")

def main():
    """主函数"""
    print("=== HuggingFace模型访问设置 ===")
    
    # 检查登录状态
    if not check_login_status():
        print("需要登录HuggingFace才能访问某些模型")
        
        choice = input("是否现在登录? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if not login_to_hf():
                print("登录失败，将使用替代模型")
    
    # 测试模型访问
    accessible_models = test_model_access()
    
    if not accessible_models:
        print("❌ 无法访问任何测试模型")
        recommend_alternative_models()
        return
    
    print(f"\n✓ 可访问的模型: {len(accessible_models)}个")
    for model in accessible_models:
        print(f"  - {model}")
    
    # 选择模型
    if "mistralai/Mistral-7B-Instruct-v0.3" in accessible_models:
        chosen_model = "mistralai/Mistral-7B-Instruct-v0.3"
        print(f"\n推荐使用: {chosen_model} (指令微调版本)")
    elif "mistralai/Mistral-7B-v0.1" in accessible_models:
        chosen_model = "mistralai/Mistral-7B-v0.1"
        print(f"\n推荐使用: {chosen_model} (基础版本)")
    else:
        chosen_model = accessible_models[0]
        print(f"\n将使用: {chosen_model}")
    
    # 更新训练脚本
    update_choice = input(f"\n是否更新训练脚本使用 {chosen_model}? (y/n): ").lower().strip()
    if update_choice in ['y', 'yes']:
        update_training_scripts(chosen_model)
    
    print(f"\n✅ 设置完成!")
    print("现在可以运行训练:")
    print("python train_simple.py")

if __name__ == "__main__":
    main()
