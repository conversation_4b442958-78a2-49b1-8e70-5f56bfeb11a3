# DeepSeek-V2 LoRA训练 - 人机协作规划任务

本项目使用LoRA方法微调DeepSeek-V2模型，用于人机协作规划任务。

## 📁 项目结构

```
ICL_LLM-main/
├── dataset/
│   ├── alpaca/                     # Alpaca格式训练数据
│   │   ├── all_alpaca_data.json   # 合并的训练数据
│   │   └── {用户名}/{场景}/        # 分用户分场景的数据
│   └── user/                      # 原始数据
├── train_lora.py                  # 主训练脚本
├── run_training.py                # 训练启动脚本
├── setup_environment.py           # 环境设置脚本
├── inference.py                   # 推理脚本
├── requirements.txt               # 依赖包列表
├── train_config.yaml             # 训练配置文件
└── README_TRAINING.md             # 本说明文件
```

## 🚀 快速开始

### 1. 环境设置

首先运行环境设置脚本：

```bash
python setup_environment.py
```

或者手动安装依赖：

```bash
pip install -r requirements.txt
```

### 2. 准备数据

确保已经运行了数据转换脚本：

```bash
python convert_to_alpaca.py
```

这将生成 `dataset/alpaca/all_alpaca_data.json` 训练文件。

### 3. 开始训练

使用简化的启动脚本：

```bash
python run_training.py
```

或者直接使用主训练脚本：

```bash
python train_lora.py \
    --model_name_or_path deepseek-ai/DeepSeek-V2 \
    --data_path dataset/alpaca/all_alpaca_data.json \
    --output_dir ./output/deepseek-v2-lora-human-robot-collaboration \
    --num_train_epochs 3 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --learning_rate 2e-4 \
    --use_lora \
    --lora_r 16 \
    --lora_alpha 32 \
    --use_4bit \
    --fp16 \
    --do_train \
    --do_eval
```

### 4. 推理测试

训练完成后，使用推理脚本测试模型：

```bash
# 运行测试用例
python inference.py --test_cases

# 交互模式
python inference.py --interactive

# 指定模型路径
python inference.py \
    --base_model deepseek-ai/DeepSeek-V2 \
    --lora_model ./output/deepseek-v2-lora-human-robot-collaboration \
    --test_cases
```

## ⚙️ 训练配置

### 模型参数
- **基础模型**: DeepSeek-V2
- **LoRA rank**: 16
- **LoRA alpha**: 32
- **LoRA dropout**: 0.1
- **目标模块**: q_proj, v_proj, k_proj, o_proj, gate_proj, up_proj, down_proj

### 训练参数
- **训练轮数**: 3
- **批次大小**: 2 (per device)
- **梯度累积**: 8步
- **学习率**: 2e-4
- **优化器**: AdamW
- **学习率调度**: Cosine
- **最大序列长度**: 4096

### 量化设置
- **4-bit量化**: 启用 (节省显存)
- **混合精度**: FP16

## 💾 硬件要求

### 最低要求
- **GPU**: 8GB显存 (RTX 3070/4060 Ti或更高)
- **内存**: 16GB RAM
- **存储**: 50GB可用空间

### 推荐配置
- **GPU**: 16GB显存 (RTX 4080/4090或A100)
- **内存**: 32GB RAM
- **存储**: 100GB SSD

## 📊 训练数据

- **总样本数**: 920个
- **用户数量**: 23个
- **场景类型**: 4种 (assembly, cleaning, cooking, sorting)
- **数据格式**: Alpaca (instruction, input, output)

### 数据示例

```json
{
  "instruction": "Generate an optimal human-robot collaboration plan based on the given scenario, constraints, and human preferences.",
  "input": "场景描述、约束条件、人类偏好等...",
  "output": "### Execution Plan:\n\n**Step 0:**\n- Human: Move to retrieve 'Base Frame'\n- Robot: Move to retrieve 'Control Chip'\n..."
}
```

## 🔧 自定义配置

### 修改训练参数

编辑 `train_config.yaml` 或在命令行中指定参数：

```bash
python train_lora.py \
    --learning_rate 1e-4 \
    --num_train_epochs 5 \
    --lora_r 32 \
    --per_device_train_batch_size 1
```

### 修改LoRA配置

```python
# 在train_lora.py中修改
lora_config = LoraConfig(
    r=32,                    # 增加rank
    lora_alpha=64,          # 增加alpha
    target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 减少目标模块
    lora_dropout=0.05,      # 减少dropout
)
```

## 📈 监控训练

### TensorBoard

```bash
tensorboard --logdir ./output/deepseek-v2-lora-human-robot-collaboration/runs
```

### 日志文件

- 训练日志: `./logs/training.log`
- 转换日志: `./convert_alpaca.log`

## 🐛 常见问题

### 1. 显存不足

```bash
# 减少批次大小
--per_device_train_batch_size 1

# 增加梯度累积
--gradient_accumulation_steps 16

# 启用梯度检查点
--gradient_checkpointing
```

### 2. 模型下载失败

```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com

# 或使用代理
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

### 3. CUDA版本不兼容

```bash
# 重新安装PyTorch
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 📝 输出文件

训练完成后，输出目录包含：

```
output/deepseek-v2-lora-human-robot-collaboration/
├── adapter_config.json          # LoRA配置
├── adapter_model.bin           # LoRA权重
├── tokenizer.json              # 分词器
├── tokenizer_config.json       # 分词器配置
├── training_args.bin           # 训练参数
└── runs/                       # TensorBoard日志
```

## 🔄 继续训练

从检查点继续训练：

```bash
python train_lora.py \
    --model_name_or_path deepseek-ai/DeepSeek-V2 \
    --resume_from_checkpoint ./output/deepseek-v2-lora-human-robot-collaboration/checkpoint-500
```

## 📚 参考资料

- [DeepSeek-V2 模型](https://huggingface.co/deepseek-ai/DeepSeek-V2)
- [LoRA论文](https://arxiv.org/abs/2106.09685)
- [PEFT库文档](https://github.com/huggingface/peft)
- [Transformers文档](https://huggingface.co/docs/transformers)
