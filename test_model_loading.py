#!/usr/bin/env python3
"""
测试模型加载脚本 - 验证DeepSeek模型是否正常工作
"""

import torch
import logging
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import os

# 设置环境变量禁用symlink警告
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tokenizer():
    """测试分词器加载"""
    logger.info("测试分词器加载...")
    
    try:
        model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        logger.info("✅ 分词器加载成功")
        
        # 测试编码
        test_text = "Hello, how are you?"
        tokens = tokenizer(test_text, return_tensors="pt")
        logger.info(f"测试编码: {test_text} -> {tokens['input_ids'].shape}")
        
        return tokenizer
        
    except Exception as e:
        logger.error(f"❌ 分词器加载失败: {e}")
        return None

def test_model_loading():
    """测试模型加载"""
    logger.info("测试模型加载...")
    
    try:
        model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
        
        # 配置4bit量化以节省显存
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        
        logger.info("开始加载模型（使用4bit量化）...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True,  # 减少CPU内存使用
        )
        
        logger.info("✅ 模型加载成功")
        logger.info(f"模型设备: {model.device}")
        
        return model
        
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None

def test_generation(model, tokenizer):
    """测试文本生成"""
    logger.info("测试文本生成...")
    
    try:
        # 简单的测试prompt
        test_prompt = "User: Hello, can you help me?\n\nAssistant: "
        
        # 编码输入
        inputs = tokenizer(test_prompt, return_tensors="pt")
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        logger.info("开始生成文本...")
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + 50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        
        # 解码输出
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取回复部分
        if "Assistant: " in response:
            response = response.split("Assistant: ", 1)[1]
        
        logger.info("✅ 文本生成成功")
        logger.info(f"生成的文本: {response}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 文本生成失败: {e}")
        return False

def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_name = torch.cuda.get_device_name(0)
        logger.info(f"GPU: {gpu_name}")
        logger.info(f"GPU内存: {gpu_memory:.1f} GB")
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        logger.info("已清理GPU缓存")
        
        return True
    else:
        logger.warning("未检测到GPU，将使用CPU")
        return False

def main():
    """主函数"""
    print("=== DeepSeek 6.7B 模型加载测试 ===")
    
    # 检查GPU
    check_gpu_memory()
    
    # 测试分词器
    tokenizer = test_tokenizer()
    if tokenizer is None:
        print("❌ 分词器测试失败")
        return
    
    # 测试模型加载
    model = test_model_loading()
    if model is None:
        print("❌ 模型加载测试失败")
        return
    
    # 测试文本生成
    success = test_generation(model, tokenizer)
    if not success:
        print("❌ 文本生成测试失败")
        return
    
    print("\n✅ 所有测试通过!")
    print("模型可以正常使用，现在可以开始训练:")
    print("python train_simple.py")

if __name__ == "__main__":
    main()
