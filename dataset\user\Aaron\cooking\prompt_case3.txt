---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- An 8m × 5m professional kitchen with multiple stovetops, ovens, and a central island. Overhead compartments hold pots and pans, fridge in the corner.
- Goal: Prepare a quick pasta meal by gathering pasta, sauce, pot, and utensils near the stove at (1,4).

[Agents]
- Human: Starts at (2,2). Robot: Starts at (5,4). The robot can move around the island in the center.

[Interactable Objects]
['Pasta Box', 'Tomato Sauce Jar', 'Cooking Pot', 'Wooden Spoon', 'Colander', 'Knife', 'Cheese Grater']
[Human Preferences]
1. I prefer to retrieve items closest to my starting position first to optimize time and movement efficiency.

2. I prioritize gathering all cooking tools and utensils before any ingredients to ensure meal preparation begins smoothly once ingredients are in place.

3. I prefer the robot to announce when an item is too heavy or cumbersome for it to handle so I can assist.

4. I favor placing frequently used items, such as the pasta and sauce, nearest to the stovetop for easy access during cooking.

5. I want to ensure the robot secures lids on jars and containers after moving or using contents, to avoid spills and maintain organization.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

