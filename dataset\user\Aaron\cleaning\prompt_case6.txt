---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 7m × 7m cafeteria area with multiple tables and chairs. The floor may have sticky spots from spilled drinks.
- Goal: Wipe tables, clear trash, and mop sticky areas on the floor.

[Agents]
- Human: Starts at (1,1). Robot: Starts at (6,6). Both can navigate freely around tables.

[Interactable Objects]
['Table Wipes', 'Trash Bags', 'Floor Mop', 'Bucket', 'Detergent', 'Hand Brush', 'Dry Towels']
[Human Preferences]
1. I prefer to clear trash first to ensure the area is tidy before wiping and mopping.

2. I prefer to use table wipes immediately after collecting them, to minimize unnecessary steps and maintain efficiency.

3. I prefer the robot to handle mopping sticky spots after I have cleared the tables to avoid any chance of re-soiling cleaned areas.

4. I prefer to gather necessary cleaning supplies before starting the cleaning process to avoid interruptions later.

5. I prefer the robot to focus on mopping areas that are farthest from the starting point to minimize travel distance for both of us.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

