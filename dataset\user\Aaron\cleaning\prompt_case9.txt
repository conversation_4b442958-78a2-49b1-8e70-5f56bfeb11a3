---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- An 8m × 8m library reading room with bookshelves and study tables. Books must be handled carefully.
- Goal: Dust shelves, organize misplaced books, and clean table surfaces.

[Agents]
- Human: Starts at (0,7). Robot: Starts at (7,0). The robot cannot handle fragile books directly.

[Interactable Objects]
['Feather Duster', 'Microfiber Cloth', 'Table Cleaner', 'Organizer Bin', 'Trash Bag', 'Vacuum with Brush Attachment', 'Soft Book Cleaner']
[Human Preferences]
1. I prefer to start by gathering all necessary cleaning tools to minimize backtracking across the room. 

2. I prefer to handle all fragile books myself and only when the required cleaning tools are within reach, ensuring minimal risk of damage.

3. I prefer to prioritize dusting the most frequently used areas first, such as study tables, to quickly create a cleaner space for immediate use.

4. I prefer to organize misplaced books on the shelves nearest to me before moving to more distant locations, reducing unnecessary movement.

5. I prefer for the trash disposal to occur at the end of the process, ensuring that all waste generated during cleaning is collected simultaneously.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

