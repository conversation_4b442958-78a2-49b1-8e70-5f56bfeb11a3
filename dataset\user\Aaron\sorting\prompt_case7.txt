---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6m × 6m multi-purpose facility with separate corners allocated for different waste streams.
- Goal: Sort and stack items into corners according to waste type, ensuring minimal cross-contamination.

[Agents]
- Robot: Located at (3, 3) with a 2m reach radius.
Human: Moves around the perimeter for quality checks.

[Interactable Objects]
['Milk Carton (Recyclable)', 'Plastic Wrap (Non-Recyclable)', 'Tin Can (Recyclable)', 'Food Waste (Compostable)', 'Broken Glass (Fragile)', 'Aluminum Foil (Recyclable)', 'Old Battery (Hazardous)']
[Human Preferences]
1. I prefer the robot to alert me with a soft beep if an object is too far out of its reach and requires human intervention.  
2. I prefer the robot to prioritize handling non-recyclable items first to minimize waste stream cross-contamination.  
3. I prefer the robot to provide an audible signal when it completes sorting a particular category of items, ensuring I'm aware of the progress.  
4. I want the robot to handle fragile items like broken glass with special care, placing them gently in designated corners to avoid breakage.  
5. I prefer to conduct a manual quality check on hazardous items like the old battery to confirm their safe placement by the robot.  
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

