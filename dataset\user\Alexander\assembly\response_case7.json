[{"step": 0, "executor": "Robot", "action": "Pick up Generator Housing"}, {"step": 0, "executor": "Human", "action": "Pick up Motor Unit"}, {"step": 1, "executor": "Human", "action": "Move to center assembly lane"}, {"step": 2, "executor": "Robot", "action": "Move Generator Housing to center lane"}, {"step": 2, "executor": "Human", "action": "Place Motor Unit in center lane"}, {"step": 3, "executor": "Human", "action": "Pick up Power Cell"}, {"step": 4, "executor": "Robot", "action": "Place Generator Housing in center lane"}, {"step": 4, "executor": "Human", "action": "Move Power Cell to center lane"}, {"step": 5, "executor": "Human", "action": "Place Power Cell in center lane"}, {"step": 6, "executor": "Robot", "action": "Pick up Control Interface"}, {"step": 6, "executor": "Human", "action": "Pick up Air Filter"}, {"step": 7, "executor": "Human", "action": "Move Air Filter to center lane"}, {"step": 8, "executor": "Robot", "action": "Move Control Interface to center lane"}, {"step": 8, "executor": "Human", "action": "Place Air Filter in center lane"}, {"step": 9, "executor": "Human", "action": "Pick up Voltage Regulator"}, {"step": 10, "executor": "Robot", "action": "Place Control Interface in center lane"}, {"step": 10, "executor": "Human", "action": "Move Voltage Regulator to center lane"}, {"step": 11, "executor": "Human", "action": "Place Voltage Regulator in center lane"}, {"step": 12, "executor": "Robot", "action": "Pick up Wheel Assembly"}, {"step": 12, "executor": "Human", "action": "Pick up Wheel Assembly"}, {"step": 13, "executor": "Human", "action": "Move Wheel Assembly to center lane"}, {"step": 14, "executor": "Robot", "action": "Move Wheel Assembly to center lane"}, {"step": 14, "executor": "Human", "action": "Place Wheel Assembly in center lane"}, {"step": 15, "executor": "Human", "action": "Assemble mobile power generator"}]