---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5 m × 7 m lab bench with a designated assembly corner and storage racks along each side.
- Goal: Assemble a small autonomous rover at coordinate (4,6).

[Agents]
- Robot: Positioned at (5,7), restricted to a perimeter loop. Human: Positioned at (0,0), restricted to a diagonal path to (5,7).

[Interactable Objects]
['Rover Chassis', 'Drive Motor', 'Battery Module', 'Sensor Array', 'Microcontroller', 'Wheel Set', 'Control Display']
[Human Preferences]
1. I prefer to start by gathering smaller components, such as the Microcontroller and Sensor Array, before assembling larger parts like the Rover Chassis to prevent disorganization on the lab bench.

2. I like to take inventory of all the components in the designated assembly corner before starting the assembly to ensure nothing is misplaced.

3. I prefer to use a specific order when connecting electrical components, such as Battery Module first before driving connections, for logical assembly flow.

4. I want to consciously avoid bottlenecks in the process, by coordinating movements with the robot efficiently, such as by waiting for it to move out of my path if needed.

5. I prefer to keep the assembly area neat and organized, only taking parts needed immediately to prevent workspace clutter.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

