[{"step": 0, "executor": "Human", "action": "Put on rubber gloves (#6)"}, {"step": 0, "executor": "Robot", "action": "Pick up non-slip mat (#5)"}, {"step": 1, "executor": "Human", "action": "Move to (2,1)"}, {"step": 1, "executor": "Robot", "action": "Move to (5,1)"}, {"step": 2, "executor": "Human", "action": "Place non-slip mat (#5) at (2,1)"}, {"step": 2, "executor": "Robot", "action": "Move to (3,1)"}, {"step": 3, "executor": "Human", "action": "Pick up bathroom cleaner spray (#2)"}, {"step": 3, "executor": "Robot", "action": "Pick up microfiber cloth (#3)"}, {"step": 4, "executor": "Human", "action": "Spray cleaner on sink at (2,1)"}, {"step": 4, "executor": "Robot", "action": "Move to (4,1)"}, {"step": 5, "executor": "Human", "action": "Spray cleaner on toilet at (4,1)"}, {"step": 5, "executor": "Robot", "action": "Move to (6,1)"}, {"step": 6, "executor": "Human", "action": "Spray cleaner on shower at (6,1)"}, {"step": 6, "executor": "Robot", "action": "Move to (7,1)"}, {"step": 7, "executor": "Human", "action": "Wipe sink with microfiber cloth (#3)"}, {"step": 7, "executor": "Robot", "action": "Wipe toilet with microfiber cloth (#3)"}, {"step": 8, "executor": "Human", "action": "Wipe shower with microfiber cloth (#3)"}, {"step": 8, "executor": "Robot", "action": "Move to (7,2)"}, {"step": 9, "executor": "Human", "action": "Pick up toilet brush (#1)"}, {"step": 9, "executor": "Robot", "action": "Pick up floor squeegee (#7)"}, {"step": 10, "executor": "Human", "action": "Scrub toilet with toilet brush (#1)"}, {"step": 10, "executor": "Robot", "action": "Squeegee floor at (7,2)"}, {"step": 11, "executor": "Human", "action": "Move to (0,1)"}, {"step": 11, "executor": "Robot", "action": "Move to (0,1)"}]