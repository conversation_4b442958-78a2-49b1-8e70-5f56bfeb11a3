[{"step": 0, "executor": "Robot", "action": "Move to Control Board #4 at (3,3)"}, {"step": 0, "executor": "Human", "action": "Move to Battery Module #3 at (1,1)"}, {"step": 1, "executor": "Human", "action": "Pick up Battery Module #3"}, {"step": 2, "executor": "Robot", "action": "Pick up Control Board #4"}, {"step": 2, "executor": "Human", "action": "Move Battery Module #3 to assembly platform"}, {"step": 3, "executor": "Human", "action": "Place Battery Module #3 at (2,2)"}, {"step": 4, "executor": "Robot", "action": "Move Control Board #4 to assembly platform"}, {"step": 4, "executor": "Human", "action": "Move to Lightweight Frame #1 at (0,1)"}, {"step": 5, "executor": "Robot", "action": "Place Control Board #4 at (2,2)"}, {"step": 6, "executor": "Human", "action": "Pick up Lightweight Frame #1"}, {"step": 6, "executor": "Robot", "action": "Move to Motor Unit #2 at (3,4)"}, {"step": 7, "executor": "Human", "action": "Move Lightweight Frame #1 to assembly platform"}, {"step": 8, "executor": "Robot", "action": "Pick up Motor Unit #2"}, {"step": 9, "executor": "Human", "action": "Place Lightweight Frame #1 at (2,2)"}, {"step": 10, "executor": "Robot", "action": "Move Motor Unit #2 to assembly platform"}, {"step": 10, "executor": "Human", "action": "Move to Navigation Sensor #7 at (1,0)"}, {"step": 11, "executor": "Human", "action": "Pick up Navigation Sensor #7"}, {"step": 12, "executor": "Robot", "action": "Place Motor Unit #2 at (2,2)"}, {"step": 12, "executor": "Human", "action": "Move Navigation Sensor #7 to assembly platform"}, {"step": 13, "executor": "Robot", "action": "Move to Rotor Blade #5 at (4,3)"}, {"step": 14, "executor": "Human", "action": "Place Navigation Sensor #7 at (2,2)"}, {"step": 15, "executor": "Robot", "action": "Pick up Rotor Blade #5"}, {"step": 16, "executor": "Human", "action": "Move to Landing Strut #6 at (0,0)"}, {"step": 17, "executor": "Robot", "action": "Move Rotor Blade #5 to assembly platform"}, {"step": 18, "executor": "Human", "action": "Pick up Landing Strut #6"}, {"step": 19, "executor": "Robot", "action": "Place Rotor Blade #5 at (2,2)"}, {"step": 20, "executor": "Human", "action": "Move Landing Strut #6 to assembly platform"}, {"step": 21, "executor": "Human", "action": "Place Landing Strut #6 at (2,2)"}]