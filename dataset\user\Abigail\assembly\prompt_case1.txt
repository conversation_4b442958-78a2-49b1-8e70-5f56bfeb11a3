---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 2 m × 2 m workstation with a small central assembly zone.
- Goal: Assemble a compact sensor unit at coordinate (1,1).

[Agents]
- Robot: Positioned at (2,2), restricted to top/right edges. Human: Positioned at (0,0), restricted to bottom/left edges.

[Interactable Objects]
['Base Frame', 'Sensor Module', 'Battery Pack', 'Mounting Bracket', 'Control Chip', 'Signal Booster', 'Status Display']
[Human Preferences]
1. I prefer to start by assembling components that are closer to my position (0,0) and progressively move towards (1,1) to minimize the time spent moving across the workstation.

2. I want the robot to hand over components that are located on the robot's side first so I can focus on assembling them while the robot fetches the remaining parts from my side.

3. I prefer working with the smaller and more intricate parts first, such as the Control Chip #5 and Signal Booster #6, since they may require more careful handling and precision.

4. I like to have the robot process tasks that involve multiple manipulations, like aligning and securing parts, while I handle straightforward assembly steps to maximize efficiency.

5. I prefer for the robot to prioritize delivering the Battery Pack #3 and Sensor Module #2 early in the assembly process, as these components are critical for testing the unit's functionality once assembled.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

