---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 2 m × 4 m narrow assembly table with two distinct shelves.
- Goal: Assemble an inspection camera rig at coordinate (1,2).

[Agents]
- Robot: Positioned at (0,4), can move along the top edge. Human: Positioned at (1,0), can move along the bottom edge.

[Interactable Objects]
['Camera Housing', 'Lens Module', 'Battery Unit', 'Mounting Arm', 'Control Chip', 'Stabilizer Gimbal', 'LED Ring']
[Human Preferences]
1. I prefer to handle smaller and more delicate components like the Lens Module #2 and Control Chip #5 myself, to ensure they are placed carefully during the assembly process.

2. I prefer to start with components that are already closer to my position, such as the Battery Unit #3, to minimize unnecessary movement across the workspace.

3. I prefer the robot to assist with heavier or more cumbersome items like the Mounting Arm #4, as moving such items manually might be cumbersome given the narrow workspace.

4. I prefer the robot to place assembled components in a systematic order on the table, ideally starting from my left to right, to help me track assembly progress easily.

5. I prefer to coordinate with the robot to create a clear assembly sequence, ensuring we have all required components close by before starting assembly at coordinate (1,2).
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

