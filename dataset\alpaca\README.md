# Alpaca格式数据集

本目录包含从原始prompt和response文件转换而来的Alpaca格式训练数据。

## 数据结构

### 目录结构
```
dataset/alpaca/
├── README.md                    # 本说明文件
├── all_alpaca_data.json        # 所有数据的合并文件
├── Aaron/                      # 用户Aaron的数据
│   ├── assembly/
│   │   ├── alpaca_case1.json
│   │   ├── alpaca_case2.json
│   │   └── ...
│   ├── cleaning/
│   ├── cooking/
│   └── sorting/
├── Abigail/                    # 用户Abigail的数据
└── ...                         # 其他用户
```

### 数据格式

每个JSON文件包含一个Alpaca格式的训练样本：

```json
{
  "instruction": "Generate an optimal human-robot collaboration plan based on the given scenario, constraints, and human preferences.",
  "input": "完整的prompt内容，包含场景描述、代理信息、对象列表、人类偏好等",
  "output": "转换后的执行计划，格式化为步骤列表"
}
```

### 字段说明

- **instruction**: 固定的指令，描述任务目标
- **input**: 原始的prompt_case{i}.txt文件内容，包含：
  - 场景描述
  - 代理约束
  - 可交互对象
  - 人类偏好
  - 动作时间规范
  - 输出格式要求
- **output**: 从response_case{i}.json转换而来的执行计划，格式化为易读的步骤列表

## 数据统计

- **总条目数**: 920个
- **用户数量**: 23个
- **场景类型**: 4个 (assembly, cleaning, cooking, sorting)
- **每用户每场景**: 10个案例
- **转换成功率**: 100%

## 使用方法

### 1. 使用单个文件
```python
import json

# 加载单个案例
with open('dataset/alpaca/Aiden/assembly/alpaca_case1.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(data['instruction'])
print(data['input'])
print(data['output'])
```

### 2. 使用合并文件
```python
import json

# 加载所有数据
with open('dataset/alpaca/all_alpaca_data.json', 'r', encoding='utf-8') as f:
    all_data = json.load(f)

print(f"总共有 {len(all_data)} 个训练样本")

# 遍历所有样本
for i, sample in enumerate(all_data):
    print(f"样本 {i+1}:")
    print(f"  指令: {sample['instruction']}")
    print(f"  输入长度: {len(sample['input'])} 字符")
    print(f"  输出长度: {len(sample['output'])} 字符")
```

### 3. 用于模型训练
```python
from datasets import Dataset
import json

# 加载数据
with open('dataset/alpaca/all_alpaca_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 转换为Hugging Face Dataset
dataset = Dataset.from_list(data)

# 查看数据集信息
print(dataset)
print(dataset[0])
```

## 转换脚本

数据转换使用以下脚本完成：
- `convert_to_alpaca.py`: 主转换脚本
- `validate_alpaca_conversion.py`: 验证脚本

## 原始数据来源

转换前的原始数据位于：
- 输入: `dataset/user/{name}/{scenario}/prompt_case{i}.txt`
- 输出: `dataset/user/{name}/{scenario}/response_case{i}.json`

## 注意事项

1. 所有文件使用UTF-8编码
2. JSON格式严格遵循Alpaca标准
3. 输出内容已从JSON格式转换为易读的文本格式
4. 保持了原始数据的完整性和结构

## 更新日志

- 2025-07-15: 初始版本，成功转换920个训练样本
