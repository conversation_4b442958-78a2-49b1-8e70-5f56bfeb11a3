# LoRA训练配置文件

# 模型参数
model_name_or_path: "deepseek-ai/DeepSeek-V2"
trust_remote_code: true
use_flash_attention_2: true

# 数据参数
data_path: "dataset/alpaca/all_alpaca_data.json"
max_train_samples: null
max_eval_samples: null

# 训练参数
output_dir: "./output/deepseek-v2-lora-human-robot-collaboration"
overwrite_output_dir: true
do_train: true
do_eval: true
evaluation_strategy: "steps"
eval_steps: 100
save_strategy: "steps"
save_steps: 500
save_total_limit: 3
load_best_model_at_end: true
metric_for_best_model: "eval_loss"
greater_is_better: false

# 训练超参数
num_train_epochs: 3
per_device_train_batch_size: 2
per_device_eval_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 2e-4
weight_decay: 0.01
warmup_ratio: 0.03
lr_scheduler_type: "cosine"
logging_steps: 10
dataloader_num_workers: 4
remove_unused_columns: false
report_to: "tensorboard"

# 模型长度
model_max_length: 4096

# LoRA参数
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1
lora_target_modules: "q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj"

# 量化参数
use_4bit: true
use_8bit: false

# 其他参数
fp16: true
gradient_checkpointing: true
optim: "adamw_torch"
seed: 42
