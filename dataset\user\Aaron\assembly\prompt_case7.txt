---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 3 m × 5 m workstation with separate left, center, and right assembly lanes.
- Goal: Assemble a mobile power generator at coordinate (2,2).

[Agents]
- Robot: Positioned at (3,5), free to move in the top row. Human: Positioned at (0,0), free to move in the bottom row.

[Interactable Objects]
['Generator Housing', 'Motor Unit', 'Power Cell', 'Control Interface', 'Air Filter', 'Voltage Regulator', 'Wheel Assembly']
[Human Preferences]
1. I prefer to interact with objects positioned closest to me first, to reduce overall movement and assembly time.

2. I prefer to handle small and lightweight components while letting the robot handle larger or heavier parts.

3. I prefer the robot to alert me with a light signal when it is holding an object to avoid collision as I move within the bottom row.

4. I prefer to assemble components in a left-to-right sequence when possible, aligning with the workstation assembly lanes.

5. I prefer to receive a verbal reminder from the robot if I need to adjust the placement of an object to meet alignment requirements at the assembly point (2,2).
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

