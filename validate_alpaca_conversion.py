import json
import os
from collections import defaultdict

def validate_alpaca_format(file_path):
    """
    验证单个Alpaca格式文件
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查必需的字段
        required_fields = ['instruction', 'input', 'output']
        for field in required_fields:
            if field not in data:
                return False, f"Missing field: {field}"
        
        # 检查字段是否为空
        for field in required_fields:
            if not data[field] or not data[field].strip():
                return False, f"Empty field: {field}"
        
        return True, "Valid"
    
    except json.JSONDecodeError:
        return False, "Invalid JSON format"
    except Exception as e:
        return False, f"Error: {str(e)}"

def analyze_alpaca_dataset():
    """
    分析转换后的Alpaca数据集
    """
    alpaca_dir = "dataset/alpaca"
    
    if not os.path.exists(alpaca_dir):
        print("Alpaca目录不存在!")
        return
    
    stats = {
        'total_files': 0,
        'valid_files': 0,
        'invalid_files': 0,
        'users': set(),
        'scenarios': set(),
        'errors': defaultdict(int)
    }
    
    print("正在验证Alpaca格式文件...")
    
    # 遍历所有用户目录
    for user_name in os.listdir(alpaca_dir):
        user_path = os.path.join(alpaca_dir, user_name)
        
        if not os.path.isdir(user_path) or user_name == 'all_alpaca_data.json':
            continue
            
        stats['users'].add(user_name)
        
        # 遍历所有场景目录
        for scenario in os.listdir(user_path):
            scenario_path = os.path.join(user_path, scenario)
            
            if not os.path.isdir(scenario_path):
                continue
                
            stats['scenarios'].add(scenario)
            
            # 遍历所有alpaca文件
            for filename in os.listdir(scenario_path):
                if filename.startswith('alpaca_case') and filename.endswith('.json'):
                    file_path = os.path.join(scenario_path, filename)
                    stats['total_files'] += 1
                    
                    is_valid, message = validate_alpaca_format(file_path)
                    
                    if is_valid:
                        stats['valid_files'] += 1
                    else:
                        stats['invalid_files'] += 1
                        stats['errors'][message] += 1
                        print(f"Invalid file: {file_path} - {message}")
    
    # 验证合并文件
    combined_file = os.path.join(alpaca_dir, "all_alpaca_data.json")
    if os.path.exists(combined_file):
        print(f"\n验证合并文件: {combined_file}")
        try:
            with open(combined_file, 'r', encoding='utf-8') as f:
                combined_data = json.load(f)
            
            if isinstance(combined_data, list):
                print(f"合并文件包含 {len(combined_data)} 个条目")
                
                # 验证前几个条目
                for i, entry in enumerate(combined_data[:5]):
                    if not all(field in entry for field in ['instruction', 'input', 'output']):
                        print(f"合并文件中第 {i+1} 个条目格式不正确")
                    else:
                        print(f"合并文件中第 {i+1} 个条目格式正确")
            else:
                print("合并文件不是列表格式")
                
        except Exception as e:
            print(f"验证合并文件时出错: {e}")
    
    # 打印统计信息
    print(f"\n=== 数据集统计 ===")
    print(f"总文件数: {stats['total_files']}")
    print(f"有效文件数: {stats['valid_files']}")
    print(f"无效文件数: {stats['invalid_files']}")
    print(f"用户数量: {len(stats['users'])}")
    print(f"场景数量: {len(stats['scenarios'])}")
    print(f"用户列表: {sorted(list(stats['users']))[:10]}...")  # 只显示前10个
    print(f"场景列表: {sorted(list(stats['scenarios']))}")
    
    if stats['errors']:
        print(f"\n=== 错误统计 ===")
        for error, count in stats['errors'].items():
            print(f"{error}: {count} 次")
    
    # 计算成功率
    if stats['total_files'] > 0:
        success_rate = (stats['valid_files'] / stats['total_files']) * 100
        print(f"\n转换成功率: {success_rate:.2f}%")

def sample_alpaca_data():
    """
    显示一些Alpaca数据样本
    """
    combined_file = "dataset/alpaca/all_alpaca_data.json"
    
    if not os.path.exists(combined_file):
        print("合并文件不存在!")
        return
    
    try:
        with open(combined_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n=== 数据样本 ===")
        print(f"总条目数: {len(data)}")
        
        # 显示第一个条目的结构
        if data:
            first_entry = data[0]
            print(f"\n第一个条目的结构:")
            print(f"- instruction长度: {len(first_entry['instruction'])} 字符")
            print(f"- input长度: {len(first_entry['input'])} 字符")
            print(f"- output长度: {len(first_entry['output'])} 字符")
            
            print(f"\ninstruction内容:")
            print(f"'{first_entry['instruction']}'")
            
            print(f"\ninput开头 (前200字符):")
            print(f"'{first_entry['input'][:200]}...'")
            
            print(f"\noutput开头 (前200字符):")
            print(f"'{first_entry['output'][:200]}...'")
    
    except Exception as e:
        print(f"读取合并文件时出错: {e}")

if __name__ == "__main__":
    analyze_alpaca_dataset()
    sample_alpaca_data()
