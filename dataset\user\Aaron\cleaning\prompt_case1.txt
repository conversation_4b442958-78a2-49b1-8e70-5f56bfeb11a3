---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6m × 4m kitchen with designated cleaning zones for cooking, storage, and waste disposal. Countertops are fragile and require gentle cleaning, while the floor is durable and can handle scrubbing.
- Goal: Clean all surfaces, dispose of waste, and store cleaning tools properly.

[Agents]
- Human: Starts at (1,2). Robot: Starts at (4,1). Both can navigate freely.

[Interactable Objects]
['Gentle Sponge', 'Dish Soap', 'Paper Towels', 'Trash Bag', 'Countertop Sanitizer', 'Scrub Brush', 'Floor Cleaner']
[Human Preferences]
1. I prefer to use the gentle sponge for cleaning the fragile countertops to avoid damage.

2. I prefer to dispose of waste using the trash bag before starting to clean surfaces, ensuring a clutter-free environment.

3. I prefer to use the floor cleaner with the scrub brush to effectively clean the durable floor surface.

4. I prefer to store cleaning tools such as the gentle sponge and scrub brush back in their designated places after the cleaning tasks are completed to maintain organization.

5. I prefer to use the countertop sanitizer to finish off the countertop cleaning, ensuring it is thoroughly sanitized.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

