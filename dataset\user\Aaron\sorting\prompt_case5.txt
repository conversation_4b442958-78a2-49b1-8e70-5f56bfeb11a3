---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 10m × 2m outdoor sorting line with minimal overhead cover.
- Goal: Separate bulky items from standard recyclables, ensuring hazardous materials are isolated.

[Agents]
- Robot: On a track at (x, 1) able to move from x=0 to x=10.
Human: Walks along the opposite side at (x, 0).

[Interactable Objects]
['Metal Drum (Bulky)', 'Plastic Jug (Recyclable)', 'Wooden Plank (Bulky)', 'Aluminum Tray (Recyclable)', 'Paper Sack (Compostable)', 'Damaged Lithium Battery (Hazardous)', 'Food Scraps (Compostable)']
[Human Preferences]
1. I prefer to first isolate any hazardous materials to immediately mitigate risk, specifically prioritizing the removal of the Damaged Lithium Battery (#6).  
   
2. I prefer that bulky items are addressed before standard recyclables to ensure clear and efficient workspace management, starting with the Metal Drum (#1) and Wooden Plank (#3).

3. I prefer to be informed by the robot if it detects any hazardous items that I might have missed in identifying, reinforcing safety measures.

4. I prefer for all compostable items to be handled after the bulky and hazardous items to streamline the sorting process, dealing with the Paper Sack (#5) and Food Scraps (#7) sequentially.

5. I prefer the robot to synchronize its movement with mine as closely as possible, especially when handling heavy or hazardous materials, to enhance teamwork and efficiency.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

