---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6m × 6m shared dormitory kitchen with personal storage lockers at (0.5,5) and a communal fridge at (5.5,1).
- Goal: Sort personal and communal items. Place personal items in the locker and communal items on the shared counter at (3,3).

[Agents]
- Human: Starts at (1,1). Robot: Starts at (4,5). The robot must avoid locker doors when open.

[Interactable Objects]
['Cereal Box', 'Milk Jug', 'Coffee Mug', 'Tea Kettle', 'Granola Bar Box', 'Fruit Basket', 'Bread Loaf']
[Human Preferences]
1. I prefer to start by collecting all personal items closest to my starting position and place them in my locker to minimize walking time.

2. I would like to prioritize placing items on the communal counter that are nearest to it, streamlining the organization process.

3. I prefer to handle all personal items myself, especially any fragile objects, to ensure they're placed in my locker securely.

4. I want to avoid working on the communal counter area while the robot is nearby to prevent any interference between us, given the limited space.

5. I prefer to have a clear, uninterrupted line of sight to my locker when placing personal items, so I can ensure they are stored correctly.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

