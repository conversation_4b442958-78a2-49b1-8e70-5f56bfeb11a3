[{"step": 0, "executor": "Human", "action": "Wear disposable gloves"}, {"step": 0, "executor": "Robot", "action": "Pick up Disinfectant Spray"}, {"step": 1, "executor": "Human", "action": "Pick up Hand Sanitizer Bottles"}, {"step": 2, "executor": "Human", "action": "Move to high-touch surface area (e.g., door handle)"}, {"step": 2, "executor": "Robot", "action": "Move to high-touch surface area (e.g., light switch)"}, {"step": 3, "executor": "Human", "action": "Disinfect high-touch surface"}, {"step": 3, "executor": "Robot", "action": "Disinfect high-touch surface"}, {"step": 4, "executor": "Human", "action": "Move to supply cabinet"}, {"step": 4, "executor": "Robot", "action": "Move to supply cabinet"}, {"step": 5, "executor": "Human", "action": "Restock cleaning supplies in cabinet"}, {"step": 5, "executor": "Robot", "action": "Restock cleaning supplies in cabinet"}, {"step": 6, "executor": "Human", "action": "Pick up Microfiber Mop Head"}, {"step": 6, "executor": "Robot", "action": "Pick up <PERSON><PERSON>"}, {"step": 7, "executor": "Human", "action": "Move to floor cleaning start point"}, {"step": 7, "executor": "Robot", "action": "Move to floor cleaning start point"}, {"step": 8, "executor": "Human", "action": "Attach Microfiber Mop Head to Mop"}, {"step": 8, "executor": "Robot", "action": "Start mopping floor"}, {"step": 9, "executor": "Human", "action": "Mop floor (parallel with robot)"}, {"step": 9, "executor": "Robot", "action": "Mop floor (parallel with human)"}, {"step": 10, "executor": "Human", "action": "Dispose of used Microfiber Mop Head"}, {"step": 10, "executor": "Robot", "action": "Dispose of used Mop"}, {"step": 11, "executor": "Human", "action": "Dispose of gloves"}, {"step": 11, "executor": "Robot", "action": "Move to Trash Bin"}, {"step": 12, "executor": "Robot", "action": "Dispose of trash"}]