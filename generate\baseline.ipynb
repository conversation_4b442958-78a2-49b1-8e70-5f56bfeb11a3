{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os \n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from openai import OpenAI\n", "import openai\n", "import tqdm\n", "import json\n", "from utils import check_complete_userdata, load_previous_steps, generate_task_description\n", "from get_response import get_response\n", "import time\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON>']\n"]}], "source": ["# user_name_list = check_complete_userdata()\n", "user_name_list = [ \"<PERSON>\"\n", "]\n", "# scenarios = [\"assembly\", \"cleaning\", \"cooking\", \"sorting\"]\n", "scenarios = [\"sorting\"]\n", "prompt_dict = json.load(open(\"prompts.json\"))\n", "print(user_name_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["percent = 0.5\n", "correct_count = 0\n", "incorrect_count = 0\n", "total_count = 0\n", "for name in tqdm.tqdm(user_name_list):\n", "    for scenario in scenarios:\n", "        for i in range(1,11):     \n", "            steps = load_previous_steps(name, scenario, i)                \n", "            previous_steps = steps[:int(len(steps)*percent)]\n", "            next_step = steps[int(len(steps)*percent)]\n", "            scenario_data = json.load(open(f\"dataset/{scenario}.json\"))\n", "            prompt = generate_task_description(scenario_data, i)\n", "            \n", "            if next_step[\"executor\"] == \"Human\":               \n", "                prompt += prompt_dict[\"base_agent\"][\"end_prompt_human\"]\n", "                prompt += f\"previous steps: {previous_steps}\\n\"\n", "                response = get_response(prompt, api_server=\"vocano\")\n", "            else:\n", "                prompt += prompt_dict[\"base_agent\"][\"end_prompt_robot\"]\n", "                prompt += f\"previous steps: {previous_steps}\\n\"\n", "                response = get_response(prompt, api_server=\"vocano\")\n", "            prompt = prompt_dict[\"compare_agent\"][\"init_prompt\"]\n", "            prompt += f\"1.{next_step}\\n 2.{response}\"\n", "            response_compare = get_response(prompt, api_server=\"vocano\")\n", "            print(response_compare)\n", "            if response_compare == \"1\":\n", "                print(f\"1.{next_step}\\n 2.{response}\")\n", "                correct_count += 1\n", "            else:\n", "                incorrect_count += 1\n", "                print(f\"1.{next_step}\\n 2.{response}\")\n", "            total_count += 1\n", "            print(f\"correct: {correct_count}, incorrect: {incorrect_count}, total: {total_count}\")\n", "        break\n", "                \n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["percent = 0.5\n", "correct_count = 0\n", "incorrect_count = 0\n", "total_count = 0\n", "for name in tqdm.tqdm(user_name_list):\n", "    for scenario in scenarios:\n", "        for i in range(1,11):     \n", "            steps = load_previous_steps(name, scenario, i)                \n", "            previous_steps = steps[:int(len(steps)*percent)]\n", "            next_step = steps[int(len(steps)*percent)]\n", "            scenario_data = json.load(open(f\"dataset/{scenario}.json\"))\n", "            prompt = generate_task_description(scenario_data, i)\n", "            prompt+=\"\\n\\n [human preference]\\n\"\n", "            #read the preference /Users/<USER>/workspace/ICL_LLM/dataset/user/Aaron/assembly/preference_case1.txt\n", "            with open(f\"dataset/user/{name}/{scenario}/preference_case{i}.txt\", \"r\") as file:\n", "                preference = file.read()\n", "            prompt += preference\n", "            if next_step[\"executor\"] == \"Human\":               \n", "                prompt += prompt_dict[\"base_agent\"][\"end_prompt_human\"]\n", "                prompt += f\"previous steps: {previous_steps}\\n\"\n", "                response = get_response(prompt, api_server=\"vocano\")\n", "            else:\n", "                prompt += prompt_dict[\"base_agent\"][\"end_prompt_robot\"]\n", "                prompt += f\"previous steps: {previous_steps}\\n\"\n", "                response = get_response(prompt, api_server=\"vocano\")\n", "            prompt = prompt_dict[\"compare_agent\"][\"init_prompt\"]\n", "            prompt += f\"1.{next_step}\\n 2.{response}\"\n", "            response_compare = get_response(prompt, api_server=\"vocano\")\n", "            print(response_compare)\n", "            if response_compare == \"1\":\n", "                print(f\"1.{next_step}\\n 2.{response}\")\n", "                correct_count += 1\n", "            else:\n", "                incorrect_count += 1\n", "                print(f\"1.{next_step}\\n 2.{response}\")\n", "            total_count += 1\n", "            print(f\"correct: {correct_count}, incorrect: {incorrect_count}, total: {total_count}\")\n", "        break\n", "                \n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for name in tqdm.tqdm(user_name_list):\n", "    for scenario in scenarios:\n", "        for i in range(1,11):\n", "            with open(f\"dataset/user/{name}/{scenario}/response_case{1}.txt\", \"r\") as file:\n", "                content = file.read()\n", "                prompt = prompt_dict[\"plan_conversion_prompt\"]\n", "                prompt = prompt + content\n", "                response = get_response(prompt, api_server=\"vocano\")\n", "                \n", "                # Clean the response by removing ```json prefix if present\n", "                cleaned_response = response\n", "                if response.strip().startswith('```json'):\n", "                    cleaned_response = response.split('```json')[-1].strip()\n", "                if cleaned_response.endswith('```'):\n", "                    cleaned_response = cleaned_response[:-3].strip()\n", "                \n", "                # save cleaned response to file\n", "                with open(f\"dataset/user/{name}/{scenario}/response_case{1}.json\", \"w\") as file:\n", "                    file.write(cleaned_response)\n", "                print(f\"save to dataset/user/{name}/{scenario}/response_case{1}.json\")\n", "            \n", "            \n", "            \n", "                \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'step': 0, 'executor': 'Human', 'action': 'Label Rechargeable Battery (Hazardous)'}, {'step': 1, 'executor': 'Human', 'action': 'Move to Glass Fragment (Fragile)'}, {'step': 2, 'executor': 'Human', 'action': 'Handle Glass Fragment (Fragile)'}, {'step': 3, 'executor': 'Human', 'action': 'Move to Plastic Cup (Recyclable)'}, {'step': 4, 'executor': 'Human', 'action': 'Label Plastic Cup (Recyclable)'}, {'step': 5, 'executor': 'Human', 'action': 'Move to Newspaper (Recyclable)'}, {'step': 6, 'executor': 'Human', 'action': 'Label Newspaper (Recyclable)'}, {'step': 7, 'executor': 'Human', 'action': 'Move to Metal Lid (Recyclable)'}, {'step': 8, 'executor': 'Human', 'action': 'Label Metal Lid (Recyclable)'}, {'step': 9, 'executor': 'Human', 'action': 'Move to Aluminum Foil (Recyclable)'}, {'step': 10, 'executor': 'Human', 'action': 'Label Aluminum Foil (Recyclable)'}, {'step': 11, 'executor': 'Human', 'action': 'Move to Rechargeable Battery (Hazardous)'}, {'step': 12, 'executor': 'Human', 'action': 'Dispose Rechargeable Battery (Hazardous)'}, {'step': 13, 'executor': 'Human', 'action': 'Move to Glass Fragment (Fragile)'}, {'step': 14, 'executor': 'Human', 'action': 'Dispose Glass Fragment (Fragile)'}, {'step': 15, 'executor': 'Robot', 'action': 'Move to Plastic Cup (Recyclable)'}, {'step': 16, 'executor': 'Robot', 'action': 'Dispose Plastic Cup (Recyclable)'}, {'step': 17, 'executor': 'Robot', 'action': 'Move to Newspaper (Recyclable)'}, {'step': 18, 'executor': 'Robot', 'action': 'Dispose Newspaper (Recyclable)'}, {'step': 19, 'executor': 'Robot', 'action': 'Move to Metal Lid (Recyclable)'}, {'step': 20, 'executor': 'Robot', 'action': 'Dispose Metal Lid (Recyclable)'}, {'step': 21, 'executor': 'Robot', 'action': 'Move to Aluminum Foil (Recyclable)'}, {'step': 22, 'executor': 'Robot', 'action': 'Dispose Aluminum Foil (Recyclable)'}]\n"]}], "source": ["import json\n", "\n", "with open('dataset/user/Aaron/sorting/response_case3.json', 'r') as file:\n", "    # Read all lines and join them\n", "    content = ''.join(line.split('|')[-1] if '|' in line else line for line in file)\n", "    # Find the JSON content between triple backticks\n", "    json_start = content.find('[')\n", "    json_end = content.rfind(']') + 1\n", "    if json_start >= 0 and json_end > 0:\n", "        json_content = content[json_start:json_end]\n", "        steps = json.loads(json_content)\n", "print(steps)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pytorch_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}