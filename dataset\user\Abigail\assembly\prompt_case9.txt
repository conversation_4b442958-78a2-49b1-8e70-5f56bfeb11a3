---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6 m × 6 m open space with four corner work areas.
- Goal: Assemble a high-capacity battery pack at coordinate (3,3).

[Agents]
- Robot: Positioned at (6,6), can move in the upper-right quadrant. Human: Positioned at (0,6), can move in the upper-left quadrant.

[Interactable Objects]
['Battery Casing', 'Power Core', 'Cooling Plate', 'Control Board', 'Fuse Assembly', 'Charging Port', 'Thermal Sensor']
[Human Preferences]
1. I prefer to move to interactable objects that are farthest from my initial position first to reduce backtracking and save time.  
2. I want the robot to notify me when it is about to enter or leave my quadrant for any handover, ensuring clear communication and coordination.  
3. I would like to keep the workspace organized by assembling components in a specific order: starting with items that involve intricate assembly, like the Power Core and Control Board, to minimize handling errors.  
4. I prefer that we prioritize assembling items that are closest to their respective corner work areas to maintain workflow efficiency and minimize movement.  
5. I enjoy keeping a sequential checklist of completed tasks and components as I move through the assembly process, allowing for a clear sense of progress and task tracking.  
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

