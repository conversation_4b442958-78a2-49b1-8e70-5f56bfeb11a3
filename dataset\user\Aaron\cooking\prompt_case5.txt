---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A spacious 7m × 4m country-style kitchen with open shelving. Wood-fired oven at (6,1), fridge at (1,1).
- Goal: Prepare bread dough by gathering flour, yeast, and utensils. Place them on the countertop at (3,2).

[Agents]
- Human: Starts at (2,3). Robot: Starts at (5,2). Both avoid the hot oven when active.

[Interactable Objects]
['Flour Sack', 'Yeast Packet', 'Mixing Bowl', 'Wooden Spoon', 'Measuring Cup', 'Dough Scraper', 'Olive Oil Bottle']
[Human Preferences]
1. I prefer to gather all ingredients and utensils from one area before moving to another to minimize travel distance and save time.

2. I prefer to place lighter and frequently used objects, like the yeast and flour, closer to the center of the workspace for easy access.

3. I prefer to start with gathering the utensils first (Mixing Bowl #3, <PERSON><PERSON> #4, Measuring Cup #5, <PERSON>h <PERSON>raper #6) before collecting ingredients, to have everything ready at hand.

4. I prefer the robot to assist in moving heavier or bulkier items, such as the Flour Sack #1, if they're too inconvenient to carry alone.

5. I prefer to keep a clear path between the fridge and the countertop at (3,2), to move freely without obstacles.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

