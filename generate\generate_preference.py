import json
import os
import random
from tqdm import tqdm
import openai
names = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Avery", "Joseph", "<PERSON>",
    "David", "Cam<PERSON>", "John", "Aria", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "Daniel", "Madison", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "Brooklyn", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>r", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>ly",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>on", "<PERSON>", "<PERSON>", "<PERSON>"
]
scenarios = [
    "assembly",
    "cooking",
    "sorting",
    "cleaning"
]
preferences = j<PERSON>.load(open("preferences.json"))
def generate_objects(num_objects, x_range, y_range, nouns):
    objects_text = ""
    for i in range(num_objects):
        noun = nouns[i]
        # Optionally, add an index suffix to ensure unique names if needed.
        object_name = f"{noun} #{i+1}"
        x = random.uniform(x_range[0], x_range[1])
        y = random.uniform(y_range[0], y_range[1])
        objects_text += f"{i+1}. {object_name} at ({x:.2f}, {y:.2f})\n"
    return objects_text
def get_task(scenario,case):
    case_data = json.load(open(f"dataset/{scenario}.json"))["cases"][str(case)]
    actions_timings_text = (
    "\n[Actions and Timings]\n"
    "- Robot Actions:\n"
    "   - move: 0.5 m/s\n"
    "   - non-moving actions: 2 second each\n"
    "- Human Actions:\n"
    "   - move: 1 m/s\n"
        "   - non-moving actions: 1 second each\n"
    )
    env_text = case_data["environment"]
    agents_text = case_data["agents"]
    goal_text = case_data["goal"]
    x_range = case_data["x_range"]
    y_range = case_data["y_range"]
    num_objects = len(case_data["object_pool"]["medium"])
    # Generate the interactable objects list
    objects_text = generate_objects(num_objects,
                                    x_range,
                                    y_range,
                                    case_data["object_pool"]["medium"])
    #open the list stored in /user/name/preference_3.txt
    general_pref = json.load(open(f"dataset/user/{name}/preference_3.txt"))
    prompt = (
        "you are a human, you have a list of general preferences, you need to generate 5 preferences for the human (your self) in the following task\n"
        "the preferences should be specific to the task and the environment\n"
        "[Scenario]\n"
        f"- {env_text}\n"
        f"- Goal: {goal_text}\n\n"
        "[Agents]\n"
        f"- {agents_text}\n\n"
        "[Interactable Objects]\n"
        f"{objects_text}\n"
        "[general preference]\n"
        f"{general_pref[0]}\n"
        f"{general_pref[1]}\n"
        f"{general_pref[2]}\n"
    )
    prompt += actions_timings_text
    prompt += "your answer should only have the prefernces with index number in front"
    
    return prompt

def check_preference_conflict(preference_person):
    # Initialize OpenAI client
    client = openai.OpenAI(api_key='***********************************************************************************************')
    
    prompt = "given the following preference list, do you think there will be a conflict of each other between the human's preferences on Human-robot collaboration?"
    for pref in preference_person:
        prompt += f"- {pref}\n"
    prompt += "Please respond with only 'yes' or 'no'"
    
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

def get_response(prompt):
    client = openai.OpenAI(api_key='***********************************************************************************************')
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content


#create a folder for each name
for name in tqdm(names):
    os.makedirs(f"dataset/user/{name}", exist_ok=True)
    # preference_person = random.choices(preferences, k=2)
    # print(check_preference_conflict(preference_person))
    #for each name create 5 scenarios folder
    #create preference.txt for 2.3.4.5 num_pref
    with open(f"dataset/user/{name}/preference_2.txt", "w") as f:
        preference_person = random.choices(preferences, k=2)
        while "yes" in check_preference_conflict(preference_person):
            preference_person = random.choices(preferences, k=2)
        f.write(json.dumps(preference_person))
    with open(f"dataset/user/{name}/preference_3.txt", "w") as f:
        preference_person = random.choices(preferences, k=3)
        while "yes" in check_preference_conflict(preference_person):
            preference_person = random.choices(preferences, k=3)
        f.write(json.dumps(preference_person))
    with open(f"dataset/user/{name}/preference_4.txt", "w") as f:
        preference_person = random.choices(preferences, k=4)
        while "yes" in check_preference_conflict(preference_person):
            preference_person = random.choices(preferences, k=4)
        f.write(json.dumps(preference_person))
    with open(f"dataset/user/{name}/preference_5.txt", "w") as f:
        preference_person = random.choices(preferences, k=5)
        while "yes" in check_preference_conflict(preference_person):
            preference_person = random.choices(preferences, k=5)
        f.write(json.dumps(preference_person))
    
    for scenario in scenarios:
        os.makedirs(f"dataset/user/{name}/{scenario}", exist_ok=True)
        #for each scenario create 10 tasks
        for i in range(1,11):
            prompt = get_task(scenario,i)
            response = get_response(prompt)
            print(response)
            with open(f"dataset/user/{name}/{scenario}/preference_case{i}.txt", "w") as f:
                f.write(response)
            
            