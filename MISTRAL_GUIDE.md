# Mistral-7B LoRA训练指南

本指南说明如何使用Mistral-7B模型进行LoRA微调，用于人机协作规划任务。

## 🤖 关于Mistral模型

### 可用的Mistral模型

1. **mistralai/Mistral-7B-Instruct-v0.3** (推荐)
   - 7B参数，指令微调版本
   - 支持多轮对话
   - 内存需求相对较低

2. **mistralai/Mistral-7B-v0.1**
   - 7B参数，基础版本
   - 需要更多的指令微调

3. **mistralai/Mixtral-8x7B-Instruct-v0.1**
   - 56B参数，专家混合模型
   - 性能更强但需要更多显存

## 📥 模型下载方式

### 方式1: 自动下载 (推荐)
代码会自动从Hugging Face下载模型，无需手动操作：

```python
# 在代码中直接使用模型名称
model_name = "mistralai/Mistral-7B-Instruct-v0.3"
```

### 方式2: 手动下载到本地
如果网络不稳定，可以先下载到本地：

```bash
# 使用git lfs下载
git lfs install
git clone https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.3

# 或使用huggingface-hub
pip install huggingface_hub
python -c "from huggingface_hub import snapshot_download; snapshot_download(repo_id='mistralai/Mistral-7B-Instruct-v0.3', local_dir='./models/Mistral-7B-Instruct-v0.3')"
```

然后在代码中使用本地路径：
```python
model_name = "./models/Mistral-7B-Instruct-v0.3"
```

## 🚀 使用方法

### 1. 直接运行 (自动下载)
```bash
python run_training.py
```

### 2. 指定本地模型路径
```bash
python train_lora.py \
    --model_name_or_path ./models/Mistral-7B-Instruct-v0.3 \
    --data_path dataset/alpaca/all_alpaca_data.json \
    --output_dir ./output/mistral-7b-lora-human-robot-collaboration
```

### 3. 使用不同的Mistral模型
```bash
# 使用Mixtral (需要更多显存)
python train_lora.py \
    --model_name_or_path mistralai/Mixtral-8x7B-Instruct-v0.1 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 16
```

## 🔧 Mistral特殊配置

### 对话格式
Mistral使用特殊的对话格式：
```
<s>[INST] 用户指令 [/INST] 模型回复</s>
```

代码已自动处理这种格式，无需手动修改。

### LoRA目标模块
Mistral-7B的推荐LoRA目标模块：
```python
lora_target_modules = [
    "q_proj", "k_proj", "v_proj", "o_proj",
    "gate_proj", "up_proj", "down_proj"
]
```

### 内存优化设置
```yaml
# 针对Mistral-7B的优化设置
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
use_4bit: true
gradient_checkpointing: true
fp16: true
```

## 💾 硬件要求

### Mistral-7B-Instruct-v0.3
- **最低**: 6GB GPU显存 (RTX 3060)
- **推荐**: 12GB GPU显存 (RTX 4070)
- **内存**: 16GB RAM

### Mixtral-8x7B-Instruct-v0.1
- **最低**: 24GB GPU显存 (RTX 4090)
- **推荐**: 40GB GPU显存 (A100)
- **内存**: 32GB RAM

## 🌐 网络配置

### 使用镜像源 (中国用户)
```bash
# 设置Hugging Face镜像
export HF_ENDPOINT=https://hf-mirror.com

# 或在代码中设置
import os
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
```

### 使用代理
```bash
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

## 📊 性能对比

| 模型 | 参数量 | 显存需求 | 训练速度 | 推理质量 |
|------|--------|----------|----------|----------|
| Mistral-7B | 7B | 6-12GB | 快 | 良好 |
| Mixtral-8x7B | 56B | 24-40GB | 慢 | 优秀 |
| DeepSeek-V2 | 236B | 40GB+ | 很慢 | 优秀 |

## 🔄 模型切换

### 从DeepSeek切换到Mistral
已完成的修改：
- ✅ 更新默认模型路径
- ✅ 修改对话格式
- ✅ 调整LoRA配置
- ✅ 更新推理脚本

### 切换到其他模型
修改以下文件中的模型路径：
1. `train_lora.py` - 第32行
2. `run_training.py` - 第46行
3. `inference.py` - 第175行
4. `train_config.yaml` - 第2行

## 🧪 测试模型

### 训练完成后测试
```bash
# 运行测试用例
python inference.py --test_cases

# 交互模式
python inference.py --interactive

# 指定模型路径
python inference.py \
    --base_model mistralai/Mistral-7B-Instruct-v0.3 \
    --lora_model ./output/mistral-7b-lora-human-robot-collaboration
```

### 示例输出
```
User: Generate an optimal human-robot collaboration plan...
