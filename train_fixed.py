#!/usr/bin/env python3
"""
修复版LoRA训练脚本 - 解决梯度计算问题
"""

import os
import json
import torch
import logging

# 设置环境变量
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:512"
os.environ["WANDB_DISABLED"] = "true"  # 禁用wandb

# 禁用wandb
import wandb
wandb.init(mode="disabled")

from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_and_prepare_data(data_path: str, tokenizer, max_length: int = 2048):
    """加载和准备数据"""
    logger.info(f"Loading data from {data_path}")
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"Loaded {len(data)} examples")
    
    # 格式化数据
    formatted_data = []
    for example in data:
        instruction = example['instruction']
        input_text = example['input']
        output_text = example['output']
        
        # 构建DeepSeek格式的对话
        if input_text.strip():
            prompt = f"User: {instruction}\n\n{input_text}\n\nAssistant: "
        else:
            prompt = f"User: {instruction}\n\nAssistant: "
        
        # 完整文本
        full_text = prompt + output_text
        
        formatted_data.append({
            "text": full_text,
            "prompt": prompt,
            "response": output_text
        })
    
    return formatted_data

def tokenize_function(examples, tokenizer, max_length):
    """分词函数"""
    # 对文本进行分词
    tokenized = tokenizer(
        examples["text"],
        truncation=True,
        padding=False,
        max_length=max_length,
        return_tensors=None,
    )
    
    # 创建labels（与input_ids相同）
    tokenized["labels"] = tokenized["input_ids"].copy()
    
    return tokenized

def load_model_and_tokenizer():
    """加载模型和分词器"""
    model_name = "deepseek-ai/deepseek-coder-6.7b-instruct"
    
    # 加载分词器
    logger.info("Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        padding_side="right",
    )
    
    # 设置pad_token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id
    
    # 配置4bit量化
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.bfloat16,  # 使用bfloat16而不是float16
    )
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 加载模型
    logger.info("Loading model...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=bnb_config,
        trust_remote_code=True,
        device_map="auto",
        torch_dtype=torch.bfloat16,  # 使用bfloat16
    )
    
    # 准备模型用于kbit训练
    model = prepare_model_for_kbit_training(model)
    
    # 配置LoRA
    lora_config = LoraConfig(
        r=16,
        lora_alpha=32,
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        lora_dropout=0.1,
        bias="none",
        task_type=TaskType.CAUSAL_LM,
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    return model, tokenizer

def main():
    """主函数"""
    logger.info("开始训练...")
    
    # 设置输出目录
    output_dir = "./output/deepseek-6.7b-lora-fixed"
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载模型和分词器
    model, tokenizer = load_model_and_tokenizer()
    
    # 加载数据
    logger.info("Loading and preparing data...")
    raw_data = load_and_prepare_data("dataset/alpaca/all_alpaca_data.json", tokenizer)
    
    # 分割数据
    train_size = int(0.9 * len(raw_data))
    train_data = raw_data[:train_size]
    eval_data = raw_data[train_size:]
    
    logger.info(f"Train dataset size: {len(train_data)}")
    logger.info(f"Eval dataset size: {len(eval_data)}")
    
    # 创建Dataset对象
    train_dataset = Dataset.from_list(train_data)
    eval_dataset = Dataset.from_list(eval_data)
    
    # 分词
    logger.info("Tokenizing data...")
    train_dataset = train_dataset.map(
        lambda x: tokenize_function(x, tokenizer, 2048),
        batched=True,
        remove_columns=train_dataset.column_names,
    )
    
    eval_dataset = eval_dataset.map(
        lambda x: tokenize_function(x, tokenizer, 2048),
        batched=True,
        remove_columns=eval_dataset.column_names,
    )
    
    # 数据收集器
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,  # 不使用masked language modeling
    )
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=output_dir,
        overwrite_output_dir=True,
        num_train_epochs=3,
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=16,
        learning_rate=2e-4,
        weight_decay=0.01,
        warmup_ratio=0.03,
        lr_scheduler_type="cosine",
        logging_steps=10,
        save_strategy="steps",
        save_steps=500,
        eval_strategy="steps",
        eval_steps=500,
        save_total_limit=2,
        remove_unused_columns=False,
        dataloader_num_workers=0,  # 设为0避免多进程问题
        fp16=False,  # 关闭fp16，使用bfloat16
        bf16=True,   # 启用bfloat16
        gradient_checkpointing=False,  # 暂时关闭梯度检查点
        optim="adamw_torch",
        seed=42,
        report_to=[],  # 明确设置为空列表
    )
    
    # 创建训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # 开始训练
    logger.info("Starting training...")
    try:
        trainer.train()
        
        # 保存模型
        logger.info("Saving model...")
        trainer.save_model()
        tokenizer.save_pretrained(output_dir)
        
        logger.info(f"Training completed! Model saved to {output_dir}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
