[{"step": 0, "executor": "Human", "action": "Move to (1,1)"}, {"step": 0, "executor": "Robot", "action": "Move to (0,7)"}, {"step": 1, "executor": "Human", "action": "Pick up Microcontroller"}, {"step": 2, "executor": "Human", "action": "Move to (2,2)"}, {"step": 3, "executor": "Human", "action": "Pick up <PERSON><PERSON>"}, {"step": 4, "executor": "Human", "action": "Move to (4,6)"}, {"step": 5, "executor": "Human", "action": "Place Microcontroller and Sensor Array"}, {"step": 5, "executor": "Robot", "action": "Move to (0,0)"}, {"step": 6, "executor": "Human", "action": "Move to (3,3)"}, {"step": 7, "executor": "Human", "action": "Pick up Battery Module"}, {"step": 8, "executor": "Human", "action": "Move to (4,6)"}, {"step": 9, "executor": "Human", "action": "Place Battery Module"}, {"step": 10, "executor": "Human", "action": "Move to (4,4)"}, {"step": 11, "executor": "Human", "action": "Pick up Drive Motor"}, {"step": 12, "executor": "Human", "action": "Move to (4,6)"}, {"step": 12, "executor": "Robot", "action": "Move to (5,7)"}, {"step": 13, "executor": "Human", "action": "Place Drive Motor"}, {"step": 14, "executor": "Human", "action": "Move to (5,5)"}, {"step": 15, "executor": "Human", "action": "Pick up Wheel Set"}, {"step": 16, "executor": "Human", "action": "Move to (4,6)"}, {"step": 17, "executor": "Human", "action": "Place Wheel Set"}, {"step": 17, "executor": "Robot", "action": "Move to (0,7)"}, {"step": 18, "executor": "Human", "action": "Move to (5,6)"}, {"step": 19, "executor": "Human", "action": "Pick up Control Display"}, {"step": 20, "executor": "Human", "action": "Move to (4,6)"}, {"step": 21, "executor": "Human", "action": "Place Control Display"}, {"step": 22, "executor": "Human", "action": "Move to (4,4)"}, {"step": 23, "executor": "Human", "action": "Pick up <PERSON>"}, {"step": 24, "executor": "Human", "action": "Move to (4,6)"}, {"step": 25, "executor": "Human", "action": "Place Rover Chassis"}]