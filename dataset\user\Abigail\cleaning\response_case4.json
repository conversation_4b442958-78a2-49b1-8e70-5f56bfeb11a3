[{"step": 0, "executor": "Human", "action": "Put on Protective Mask #7"}, {"step": 0, "executor": "Human", "action": "Put on Heavy-Duty Gloves #3"}, {"step": 1, "executor": "Robot", "action": "Move to Industrial Vacuum #1"}, {"step": 2, "executor": "Human", "action": "Move to Magnetic Sweeper #2"}, {"step": 3, "executor": "Human", "action": "Pick up Magnetic Sweeper #2"}, {"step": 3, "executor": "Robot", "action": "Pick up Industrial Vacuum #1"}, {"step": 4, "executor": "Human", "action": "Use Magnetic Sweeper #2 to clear shavings"}, {"step": 4, "executor": "Robot", "action": "Move to workstation near (3,2)"}, {"step": 5, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 5, "executor": "Human", "action": "Move to Degreaser #4"}, {"step": 6, "executor": "Human", "action": "Pick up Degreaser #4"}, {"step": 6, "executor": "Robot", "action": "Move to next workstation near (4,1)"}, {"step": 7, "executor": "Human", "action": "Use Degreaser #4 to clean workstation"}, {"step": 7, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 8, "executor": "Human", "action": "Move to <PERSON><PERSON> #5"}, {"step": 8, "executor": "Robot", "action": "Move to next workstation near (5,1)"}, {"step": 9, "executor": "Human", "action": "Pick up <PERSON><PERSON> #5"}, {"step": 9, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 10, "executor": "Human", "action": "Use Rag Cloth #5 to wipe down workstation"}, {"step": 10, "executor": "Robot", "action": "Move to Dustpan and Brush #6"}, {"step": 11, "executor": "Robot", "action": "Pick up <PERSON><PERSON> and Brush #6"}, {"step": 11, "executor": "Human", "action": "Move to next workstation near (6,2)"}, {"step": 12, "executor": "Robot", "action": "Use <PERSON><PERSON> and <PERSON><PERSON> #6 for final cleanup"}, {"step": 12, "executor": "Human", "action": "Move to next workstation near (7,3)"}, {"step": 13, "executor": "Human", "action": "Use Magnetic Sweeper #2 to clear shavings"}, {"step": 13, "executor": "Robot", "action": "Move to next workstation near (7,3)"}, {"step": 14, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 14, "executor": "Human", "action": "Move to Degreaser #4"}, {"step": 15, "executor": "Human", "action": "Pick up Degreaser #4"}, {"step": 15, "executor": "Robot", "action": "Move to next workstation near (8,3)"}, {"step": 16, "executor": "Human", "action": "Use Degreaser #4 to clean workstation"}, {"step": 16, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 17, "executor": "Human", "action": "Move to <PERSON><PERSON> #5"}, {"step": 17, "executor": "Robot", "action": "Move to next workstation near (9,3)"}, {"step": 18, "executor": "Human", "action": "Pick up <PERSON><PERSON> #5"}, {"step": 18, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 19, "executor": "Human", "action": "Use Rag Cloth #5 to wipe down workstation"}, {"step": 19, "executor": "Robot", "action": "Move to Dustpan and Brush #6"}, {"step": 20, "executor": "Robot", "action": "Pick up <PERSON><PERSON> and Brush #6"}, {"step": 20, "executor": "Human", "action": "Move to next workstation near (10,4)"}, {"step": 21, "executor": "Robot", "action": "Use <PERSON><PERSON> and <PERSON><PERSON> #6 for final cleanup"}, {"step": 21, "executor": "Human", "action": "Move to next workstation near (10,5)"}, {"step": 22, "executor": "Human", "action": "Use Magnetic Sweeper #2 to clear shavings"}, {"step": 22, "executor": "Robot", "action": "Move to next workstation near (10,5)"}, {"step": 23, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 23, "executor": "Human", "action": "Move to Degreaser #4"}, {"step": 24, "executor": "Human", "action": "Pick up Degreaser #4"}, {"step": 24, "executor": "Robot", "action": "Move to next workstation near (9,5)"}, {"step": 25, "executor": "Human", "action": "Use Degreaser #4 to clean workstation"}, {"step": 25, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 26, "executor": "Human", "action": "Move to <PERSON><PERSON> #5"}, {"step": 26, "executor": "Robot", "action": "Move to next workstation near (8,5)"}, {"step": 27, "executor": "Human", "action": "Pick up <PERSON><PERSON> #5"}, {"step": 27, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 28, "executor": "Human", "action": "Use Rag Cloth #5 to wipe down workstation"}, {"step": 28, "executor": "Robot", "action": "Move to Dustpan and Brush #6"}, {"step": 29, "executor": "Robot", "action": "Pick up <PERSON><PERSON> and Brush #6"}, {"step": 29, "executor": "Human", "action": "Move to next workstation near (7,5)"}, {"step": 30, "executor": "Robot", "action": "Use <PERSON><PERSON> and <PERSON><PERSON> #6 for final cleanup"}, {"step": 30, "executor": "Human", "action": "Move to next workstation near (6,5)"}, {"step": 31, "executor": "Human", "action": "Use Magnetic Sweeper #2 to clear shavings"}, {"step": 31, "executor": "Robot", "action": "Move to next workstation near (6,5)"}, {"step": 32, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 32, "executor": "Human", "action": "Move to Degreaser #4"}, {"step": 33, "executor": "Human", "action": "Pick up Degreaser #4"}, {"step": 33, "executor": "Robot", "action": "Move to next workstation near (5,5)"}, {"step": 34, "executor": "Human", "action": "Use Degreaser #4 to clean workstation"}, {"step": 34, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 35, "executor": "Human", "action": "Move to <PERSON><PERSON> #5"}, {"step": 35, "executor": "Robot", "action": "Move to next workstation near (4,5)"}, {"step": 36, "executor": "Human", "action": "Pick up <PERSON><PERSON> #5"}, {"step": 36, "executor": "Robot", "action": "Use Industrial Vacuum #1 to clean shavings"}, {"step": 37, "executor": "Human", "action": "Use Rag Cloth #5 to wipe down workstation"}, {"step": 37, "executor": "Robot", "action": "Move to Dustpan and Brush #6"}, {"step": 38, "executor": "Robot", "action": "Pick up <PERSON><PERSON> and Brush #6"}, {"step": 38, "executor": "Human", "action": "Move to next workstation near (3,5)"}, {"step": 39, "executor": "Robot", "action": "Use <PERSON><PERSON> and <PERSON><PERSON> #6 for final cleanup"}]