---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 4m × 3m kitchen with a single working counter near the window. The fridge is at (0.5,1.0) and the pantry at (3.5,2.5).
- Goal: Organize baking ingredients into labeled containers on the window-side countertop.

[Agents]
- Human: Starts at (2,2). Robot: Starts at (1,1). Robot has limited movement due to a sink in the corner (2,1).

[Interactable Objects]
['Flour Bag', 'Sugar Canister', 'Egg Carton', 'Mixing Bowl', 'Whisk', 'Measuring Cup', 'Cookie Sheet']
[Human Preferences]
1. I prefer to organize the ingredients starting with the items closest to the countertop to minimize unnecessary movement. 

2. I prefer the robot to communicate before moving past me to avoid unexpected paths in the narrow kitchen space.

3. I prefer to place frequently used items, such as the sugar canister (#2), toward the edge of the countertop where they are easily accessible.

4. I prefer to keep the path between the fridge and the countertop clear, prioritizing the placement of ingredients accordingly.

5. I prefer the robot to handle items over the sink area cautiously, as space is restricted there.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

