[{"step": 0, "executor": "Human", "action": "Communicate with robot about handling 'Pressure Gauge'"}, {"step": 0, "executor": "Robot", "action": "Communicate with human about handling 'Pressure Gauge'"}, {"step": 1, "executor": "Human", "action": "Move to 'Pressure Gauge' at (1,6)"}, {"step": 1, "executor": "Robot", "action": "Move to 'Valve System' at (4,5)"}, {"step": 2, "executor": "Human", "action": "Pick up 'Pressure Gauge'"}, {"step": 3, "executor": "Robot", "action": "Pick up 'Valve System'"}, {"step": 4, "executor": "Human", "action": "Move to assembly point (2,3)"}, {"step": 5, "executor": "Robot", "action": "Move to assembly point (2,3)"}, {"step": 6, "executor": "Human", "action": "Assemble 'Pressure Gauge'"}, {"step": 7, "executor": "Robot", "action": "Assemble 'Valve System'"}, {"step": 8, "executor": "Human", "action": "Communicate with robot about handling 'Control Switch'"}, {"step": 8, "executor": "Robot", "action": "Communicate with human about handling 'Control Switch'"}, {"step": 9, "executor": "Human", "action": "Move to 'Control Switch' at (3,6)"}, {"step": 9, "executor": "Robot", "action": "Move to 'Reservoir Tank' at (4,4)"}, {"step": 10, "executor": "Human", "action": "Pick up 'Control Switch'"}, {"step": 11, "executor": "Robot", "action": "Pick up 'Reservoir Tank'"}, {"step": 12, "executor": "Human", "action": "Move to assembly point (2,3)"}, {"step": 13, "executor": "Robot", "action": "Move to assembly point (2,3)"}, {"step": 14, "executor": "Human", "action": "Assemble 'Control Switch'"}, {"step": 15, "executor": "Robot", "action": "Assemble 'Reservoir Tank'"}, {"step": 16, "executor": "Human", "action": "Communicate with robot about handling 'Cylinder Unit'"}, {"step": 16, "executor": "Robot", "action": "Communicate with human about handling 'Cylinder Unit'"}, {"step": 17, "executor": "Human", "action": "Move to 'Cylinder Unit' at (4,6)"}, {"step": 17, "executor": "Robot", "action": "Move to 'Hydraulic Pump' at (4,3)"}, {"step": 18, "executor": "Human", "action": "Pick up 'Cylinder Unit'"}, {"step": 19, "executor": "Robot", "action": "Pick up 'Hydraulic Pump'"}, {"step": 20, "executor": "Human", "action": "Move to assembly point (2,3)"}, {"step": 21, "executor": "Robot", "action": "Move to assembly point (2,3)"}, {"step": 22, "executor": "Human", "action": "Assemble 'Cylinder Unit'"}, {"step": 23, "executor": "Robot", "action": "Assemble 'Hydraulic Pump'"}, {"step": 24, "executor": "Human", "action": "Communicate with robot about handling 'Press Base'"}, {"step": 24, "executor": "Robot", "action": "Communicate with human about handling 'Press Base'"}, {"step": 25, "executor": "Human", "action": "Move to 'Press Base' at (5,6)"}, {"step": 25, "executor": "Robot", "action": "Move to 'Press Base' at (5,3)"}, {"step": 26, "executor": "Human", "action": "Pick up 'Press Base'"}, {"step": 27, "executor": "Robot", "action": "Pick up 'Press Base'"}, {"step": 28, "executor": "Human", "action": "Move to assembly point (2,3)"}, {"step": 29, "executor": "Robot", "action": "Move to assembly point (2,3)"}, {"step": 30, "executor": "Human", "action": "Assemble 'Press Base'"}, {"step": 31, "executor": "Robot", "action": "Assemble 'Press Base'"}]