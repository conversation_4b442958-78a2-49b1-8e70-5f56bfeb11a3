---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 4 m × 4 m open floor with a single assembly platform in the middle.
- Goal: Assemble a prototype drone chassis at coordinate (2,2).

[Agents]
- Robot: Starts at (4,4), free to move in a 2×2 subarea at the top-right. Human: Starts at (0,0), free to move in a 2×2 subarea at bottom-left.

[Interactable Objects]
['Lightweight Frame', 'Motor Unit', 'Battery Module', 'Control Board', 'Rotor Blade', 'Landing Strut', 'Navigation Sensor']
[Human Preferences]
1. I prefer to handle the Battery Module #3 myself since it is closer to my starting position and requires swift movement.

2. I prefer the robot to prioritize handling objects in its assigned area, particularly Motor Unit #2 and Rotor Blade #5 which are in the upper region.

3. I prefer real-time communication with the robot, especially when moving objects to the assembly platform, to ensure seamless coordination and avoid congestion.

4. I prefer the robot to initiate assembly by bringing the Control Board #4 first, since it provides a base for connecting other components.

5. I prefer to finish assembling lightweight components like Lightweight Frame #1 and Navigation Sensor #7 before assembling heavier components to minimize risk and ensure stability.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

