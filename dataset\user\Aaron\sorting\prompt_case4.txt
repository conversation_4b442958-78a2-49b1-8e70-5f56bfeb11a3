---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6m × 3m corridor with separate lanes for robot and human movement.
- Goal: Classify and place items onto the correct conveyor belt segment (fragile, recyclable, compostable).

[Agents]
- Robot: Moves along a fixed rail at y=1.5.
Human: Free to walk within a 2m zone along the corridor sides.

[Interactable Objects]
['Paper Bag (Compostable)', 'Soda Can (Recyclable)', 'Glass Bottle (Fragile)', 'Plastic Container (Recyclable)', 'Bioplastic Cup (Compostable)', 'Large Cardboard (Recyclable)', 'Used Towel (Compostable)']
[Human Preferences]
1. I prefer to start with the objects that are closest to me and work my way down the corridor, minimizing back-and-forth movement.

2. I prefer to classify fragile items, such as the glass bottle, first to ensure they are handled with care and placed out of harm's way quickly.

3. I prefer the robot to handle items that are along its path or closer to its lane, allowing me to focus on the ones on the opposite side for efficient task completion.

4. I prefer to communicate with the robot verbally when I approach an intersection or shared space, ensuring we coordinate our actions without any confusion or collision.

5. I prefer the robot to alert me if I mistakenly classify an object, so I can reassess and correct the assignment promptly.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

