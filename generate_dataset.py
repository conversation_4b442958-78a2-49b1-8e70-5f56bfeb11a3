import json
import os
import random
from tqdm import tqdm
import openai
from openai import OpenAI
import logging
from utils import check_complete_userdata, load_previous_steps, generate_task_description

OPENAI_API_KEY = 'f03c5260-8425-465c-b6c8-c929568a7e60'
logging.basicConfig(filename='./prompt_response.log',level=logging.INFO)

# names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
names = [
 "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
"<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",  
"<PERSON>",  
"<PERSON>", "<PERSON>", "<PERSON>", "Sebastian", "Skylar", "Sofia", "Sophia", "Stella",  
"Theodore", "Thomas",  
"Victoria", "Violet",  
"William", "Wyatt",  
"Xavier",  
"Zoey", "Zoe"
]

scenarios = [
    "assembly",
    "cleaning",
    "cooking",
    "sorting",
]

end_prompt = """
**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

"""

# Actions and timing definitions for both agents
actions_timings_text = (
    "\n[Actions and Timings]\n"
    "- Robot Actions:\n"
    "   - move: 0.5 m/s\n"
    "   - non-moving actions: 2 second each\n"
    "- Human Actions:\n"
    "   - move: 1 m/s\n"
    "   - non-moving actions: 1 second each\n"
)
def call_api(PROMPT):

    # client = OpenAI(api_key="sk-oszbybewsjzeoddkjokwptfmmirtjlxsxsjmzcgzldqqjuvh", base_url="https://api.siliconflow.cn/v1")
    client = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key=os.environ.get("OPENAI_API_KEY"),
    )
    # deepseek-ai/DeepSeek-V2.5
    # deepseek-ai/DeepSeek-R1
    response = client.chat.completions.create(
        model='deepseek-v3-241226', #deepseek-ai/DeepSeek-V2.5
        messages=[
            {'role': 'user', 
            'content': PROMPT}
        ]
    )   
    return response.choices[0].message.content  


def get_response(prompt, api_server="vocanico"):
    if api_server == "vocanico":
        # HWC 
        # client = openai.OpenAI(api_key='ozc3yLyJvwXLiSKOdUJ7nmhSawoG6xLenpfSPHql6DsJ-OI8LizctDjTiorsKAJ1RadKLRTl3PkbE09RztRQzA', base_url="https://infer-modelarts-cn-southwest-2.modelarts-infer.com/v1/infers/fd53915b-8935-48fe-be70-449d76c0fc87/v1")
        client = OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("OPENAI_API_KEY"),
        )
        response = client.chat.completions.create(
            model="deepseek-v3-241226", #DeepSeek-V3
            messages=[{"role": "user", "content": prompt}],
            temperature=1.0
        )
        return response.choices[0].message.content


def generate_prompt(scenario_data, case, prefs):
    # Get details for the chosen scenario and case
    # print(scenario_data["cases"])
    case_data = scenario_data["cases"][str(case)]
    env_text = case_data["environment"]
    agents_text = case_data["agents"]
    goal_text = case_data["goal"]
    x_range = case_data["x_range"]
    y_range = case_data["y_range"]
    objects_text = case_data["object_pool"]["medium"]
    # Generate the interactable objects list
    # Assemble the full prompt
    prompt = (
        "---\n"
        "You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.\n"
        "[Scenario]\n"
        f"- {env_text}\n"
        f"- Goal: {goal_text}\n\n"
        "[Agents]\n"
        f"- {agents_text}\n\n"
        "[Interactable Objects]\n"
        f"{objects_text}\n"
        "[Human Preferences]\n"
    )
    prompt += prefs
    prompt += actions_timings_text
    prompt += end_prompt
    return prompt

for name in tqdm(names):
    logging.info(f" {name} >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    for scenario in scenarios:
        logging.info(f" {name} | {scenario} >>>>>>>>>>>>>>")
        for i in range(1,11):
            scenario_data = json.load(open(f"dataset/{scenario}.json", encoding="utf-8"))
            #load txt to string
            logging.info(f" Open: dataset/user/{name}/{scenario}/preference_case{i}.txt ")
            prefs = open(f"dataset/user/{name}/{scenario}/preference_case{i}.txt", encoding="utf-8").read()
            prompt = generate_prompt(scenario_data, i, prefs)
            with open(f"dataset/user/{name}/{scenario}/prompt_case{i}.txt", "w", encoding="utf-8") as f:
                f.write(prompt) 
                logging.info(f" Generated: dataset/user/{name}/{scenario}/prompt_case{i}.txt ")
            response = get_response(prompt)
            with open(f"dataset/user/{name}/{scenario}/response_case{i}.txt", "w", encoding="utf-8") as f:
                f.write(response)
                logging.info(f" Generated: dataset/user/{name}/{scenario}/response_case{i}.txt")
            # prompt_post = "organize the plan into a json format with only step number and Executor and Action row, your answer should only have the code nothing else. If Time Interval (s) is the same, the step number should also be the same. \n here is the plan\n"
            # prompt_post += response
            # response_post = get_response(prompt_post)
            # with open(f"dataset/user/{name}/{scenario}/response_case{i}.json", "w", encoding="utf-8") as f:
            #     f.write(response_post)
            #     logging.info(f" Generated: dataset/user/{name}/{scenario}/response_case{i}.json")
                
