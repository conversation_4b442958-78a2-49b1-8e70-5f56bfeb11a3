---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A narrow 4m × 6m galley kitchen with two parallel counters. Stove at (1,2) and fridge at (3,5).
- Goal: Gather soup ingredients and place them on the stove-side counter at (1,1.5).

[Agents]
- Human: Starts at (0.5,5). Robot: Starts at (3.5,0.5). The robot moves along the corridor between counters.

[Interactable Objects]
['Stock Pot', 'Soup Spoon', 'Broth Carton', 'Onion', 'Celery', 'Salt Shaker', 'Pepper Grinder']
[Human Preferences]
1. I prefer to gather the lighter objects first, such as the pepper grinder and soup spoon, to minimize fatigue and maximize efficiency.

2. I prefer to coordinate with the robot to ensure that we do not block each other in the narrow kitchen space, especially near intersections or when approaching the fridge.

3. I prefer to keep a clear line of sight to the counters as much as possible to quickly identify the next ingredient I need to gather.

4. I prefer to place the gathered ingredients in a stable manner on the stove-side counter to prevent them from rolling or falling off.

5. I prefer to know the robot’s complete planned path to ensure that I can avoid areas it may frequently travel through, such as near the fridge and stove.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

