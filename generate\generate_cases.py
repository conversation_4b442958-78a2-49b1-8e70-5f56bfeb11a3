import argparse
import random
import json
end_prompt = """
**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or <PERSON>) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and <PERSON>’s preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

"""

# Shared human preferences across all tasks
shared_preferences = [
    "Minimize overall movement.",
    "Enable concurrent actions where possible.",
    "Avoid unnecessary backtracking.",
    "Maintain safety at all times.",
    "Ensure effective communication with the robot."
]

# Actions and timing definitions for both agents
actions_timings_text = (
    "\n[Actions and Timings]\n"
    "- Robot Actions:\n"
    "   - move: 0.5 m/s\n"
    "   - non-moving actions: 2 second each\n"
    "- Human Actions:\n"
    "   - move: 1 m/s\n"
    "   - non-moving actions: 1 second each\n"
)

def generate_objects(num_objects, x_range, y_range, nouns):
    objects_text = ""
    for i in range(num_objects):
        noun = nouns[i]
        # Optionally, add an index suffix to ensure unique names if needed.
        object_name = f"{noun} #{i+1}"
        x = random.uniform(x_range[0], x_range[1])
        y = random.uniform(y_range[0], y_range[1])
        objects_text += f"{i+1}. {object_name} at ({x:.2f}, {y:.2f})\n"
    return objects_text

def generate_prompt(scenario_data, case, num_case_prefs, num_shared_prefs, ordering,args):
    # Get details for the chosen scenario and case
    # print(scenario_data["cases"])
    case_data = scenario_data["cases"][str(case)]
    env_text = case_data["environment"]
    agents_text = case_data["agents"]
    goal_text = case_data["goal"]
    x_range = case_data["x_range"]
    y_range = case_data["y_range"]
    num_objects = len(case_data["object_pool"][args.lv_obj])
    # Generate the interactable objects list
    objects_text = generate_objects(num_objects,
                                    x_range,
                                    y_range,
                                    case_data["object_pool"][args.lv_obj])
    
    # Select preferences (slicing in case the requested number is less than available)
    scenario_prefs = case_data["preferences"][:num_case_prefs]
    shared_prefs = shared_preferences[:num_shared_prefs]
    
    # Determine prioritization statement
    if ordering == "time":
        ordering_text = "Preference Prioritization: Time is prioritized over human preferences."
    elif ordering == "preference":
        ordering_text = "Preference Prioritization: Human preferences are prioritized over time."
    else:
        ordering_text = "Preference Prioritization: A balanced approach is taken between time efficiency and human preferences."
    
    # Assemble the full prompt
    prompt = (
        "---\n"
        "You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human (John) to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—John’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.\n"
        "[Scenario]\n"
        f"- {env_text}\n"
        f"- Goal: {goal_text}\n\n"
        "[Agents]\n"
        f"- {agents_text}\n\n"
        "[Interactable Objects]\n"
        f"{objects_text}\n"
        "[Human Preferences]\n"
        "Scenario-Specific Preferences:\n"
    )
    for pref in scenario_prefs:
        prompt += f"- {pref}\n"
    prompt += "\nShared Preferences:\n"
    for pref in shared_prefs:
        prompt += f"- {pref}\n"
    prompt += f"\n{ordering_text}\n"
    prompt += actions_timings_text
    prompt += end_prompt
    return prompt

def main():
    parser = argparse.ArgumentParser(description="Generate a human-robot collaboration prompt.")
    parser.add_argument("--scenario", choices=["sorting", "assembly", "cooking", "cleaning"], required=True,
                        help="Choose the scenario.")
    parser.add_argument("--case", type=int, required=True,
                        help="Choose the case number for the scenario.")
    parser.add_argument("--num_case_preferences", type=int, default=3,
                        help="Number of scenario-specific human preferences to include.")
    parser.add_argument("--num_shared_preferences", type=int, default=1,
                        help="Number of shared human preferences to include.")
    parser.add_argument("--lv_obj", choices=["easy", "medium", "hard"], default="easy",
                        help="Choose the level of difficulty for the case objects.")
    parser.add_argument("--ordering", choices=["time", "preference", "balanced"], default="balanced",
                        help="Define whether 'time' is prioritized over 'preference', vice versa, or a balanced approach.")
    args = parser.parse_args()
    scenario_data = json.load(open(f"scenarios/{args.scenario}.json"))
    prompt_text = generate_prompt(scenario_data, args.case,
                                  args.num_case_preferences, args.num_shared_preferences,
                                  args.ordering,args)
    print(prompt_text)

if __name__ == "__main__":
    main()
