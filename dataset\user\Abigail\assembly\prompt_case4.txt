---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 2 m × 3 m workstation with separate staging areas on the left and right.
- Goal: Assemble a basic conveyor sub-unit at coordinate (1,1).

[Agents]
- Robot: Positioned at (1,3), moves along the top row. Human: Positioned at (0,0), moves within the bottom row.

[Interactable Objects]
['Conveyor Frame', 'Roller', 'Motor Unit', 'Control Panel', 'Power Supply', 'Speed Sensor', 'Mounting Plate']
[Human Preferences]
1. I prefer the robot to prioritize handing over the control panel (#4) first, as it is closer to my position at (0,0) and I can efficiently incorporate it into the assembly.

2. I prefer the robot to inform me when I am standing in the way of its movement path along the top row, so I can adjust my position accordingly and maintain a smooth workflow.

3. I want the robot to use its delicate grip when handling the Speed Sensor (#6) since precise components like sensors can be more fragile and require careful handling.

4. I prefer to receive objects in the order of their proximity to the goal at (1,1) to minimize unnecessary movement during assembly and to streamline the process.

5. I like the robot to keep me updated every two items picked and placed, giving a quick verbal summary of what items have been handled, reducing potential confusion during the assembly process.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

