---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5m × 2m mobile sorting trailer with a single conveyor belt running along its length.
- Goal: Separate compostable materials from recyclables, setting aside hazardous waste in a locked container.

[Agents]
- Robot: Fixed at the center (2.5, 1) with a rotating arm to reach both sides.
Human: Enters from (0, 1) for periodic checks.

[Interactable Objects]
['Orange Peel (Compostable)', 'Plastic Bottle (Recyclable)', 'Paper Towel (Compostable)', 'Metal Spoon (Recyclable)', 'Broken Ceramic (Fragile)', 'Ink Cartridge (Hazardous)', 'Cardboard Sheet (Recyclable)']
[Human Preferences]
1. I prefer to start the task by first identifying and securing hazardous materials, like the Ink Cartridge (#6), to ensure safety before proceeding with other tasks.

2. I prefer that all compostable items, such as the Orange Peel (#1) and Paper Towel (#3), be sorted together before moving on to recyclable materials for efficiency.

3. I prefer to use a sequence where small and lightweight recyclable objects, like the Plastic Bottle (#2) and Metal Spoon (#4), are handled before moving on to bulky items like the Cardboard Sheet (#7), to keep the workspace manageable.

4. I prefer to check the condition of fragile items, like the Broken Ceramic (#5), as quickly as possible to prevent accidental damage or dangerous situations before sorting them.

5. I prefer the robot to give a summary of completed tasks after each periodic check to maintain a clear understanding of progress and ensure no tasks are missed.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

