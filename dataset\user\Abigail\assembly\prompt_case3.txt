---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 4 m × 4 m open floor with a single assembly platform in the middle.
- Goal: Assemble a prototype drone chassis at coordinate (2,2).

[Agents]
- Robot: Starts at (4,4), free to move in a 2×2 subarea at the top-right. Human: Starts at (0,0), free to move in a 2×2 subarea at bottom-left.

[Interactable Objects]
['Lightweight Frame', 'Motor Unit', 'Battery Module', 'Control Board', 'Rotor Blade', 'Landing Strut', 'Navigation Sensor']
[Human Preferences]
1. I prefer to start assembling with the objects located closer to my start position, such as the Control Board #4, to minimize initial movement.  
2. I want the robot to prioritize bringing items that are outside of my free movement area, especially if they are closer to the robot, like Motor Unit #2 or Battery Module #3.  
3. I like the robot to assist in positioning and holding items steady while I secure them in place, especially larger items like the Lightweight Frame #1.  
4. I prefer to handle and assemble fragile items myself to ensure they are managed with care, while the robot focuses on heavier, more robust components.  
5. I want the robot to notify me when it has completed its task of delivering the requested item to avoid time spent checking on its progress.  
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

