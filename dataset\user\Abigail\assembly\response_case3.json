[{"step": 0, "executor": "Human", "action": "Move to Control Board #4"}, {"step": 0, "executor": "Robot", "action": "Move to Motor Unit #2"}, {"step": 1, "executor": "Human", "action": "Pick up Control Board #4"}, {"step": 2, "executor": "Human", "action": "Move to Assembly Platform (2,2)"}, {"step": 3, "executor": "Robot", "action": "Pick up Motor Unit #2"}, {"step": 4, "executor": "Human", "action": "Place Control Board #4 at Assembly Platform"}, {"step": 5, "executor": "Robot", "action": "Move to Assembly Platform (2,2)"}, {"step": 6, "executor": "Robot", "action": "Place Motor Unit #2 at Assembly Platform"}, {"step": 7, "executor": "Human", "action": "Secure Motor Unit #2"}, {"step": 8, "executor": "Robot", "action": "Move to Battery Module #3"}, {"step": 9, "executor": "Robot", "action": "Pick up Battery Module #3"}, {"step": 10, "executor": "Robot", "action": "Move to Assembly Platform (2,2)"}, {"step": 11, "executor": "Robot", "action": "Place Battery Module #3 at Assembly Platform"}, {"step": 12, "executor": "Human", "action": "Secure Battery Module #3"}, {"step": 13, "executor": "Robot", "action": "Move to Lightweight Frame #1"}, {"step": 14, "executor": "Robot", "action": "Pick up Lightweight Frame #1"}, {"step": 15, "executor": "Robot", "action": "Move to Assembly Platform (2,2)"}, {"step": 16, "executor": "Robot", "action": "Place Lightweight Frame #1 at Assembly Platform"}, {"step": 17, "executor": "Human", "action": "Secure Lightweight Frame #1"}, {"step": 18, "executor": "Human", "action": "Move to Rotor Blade #5"}, {"step": 19, "executor": "Human", "action": "Pick up Rotor Blade #5"}, {"step": 20, "executor": "Human", "action": "Move to Assembly Platform (2,2)"}, {"step": 21, "executor": "Human", "action": "Place Rotor Blade #5 at Assembly Platform"}, {"step": 22, "executor": "Human", "action": "Secure Rotor Blade #5"}, {"step": 23, "executor": "Human", "action": "Move to Landing Strut #6"}, {"step": 24, "executor": "Human", "action": "Pick up Landing Strut #6"}, {"step": 25, "executor": "Human", "action": "Move to Assembly Platform (2,2)"}, {"step": 26, "executor": "Human", "action": "Place Landing Strut #6 at Assembly Platform"}, {"step": 27, "executor": "Human", "action": "Secure Landing Strut #6"}, {"step": 28, "executor": "Human", "action": "Move to Navigation Sensor #7"}, {"step": 29, "executor": "Human", "action": "Pick up Navigation Sensor #7"}, {"step": 30, "executor": "Human", "action": "Move to Assembly Platform (2,2)"}, {"step": 31, "executor": "Human", "action": "Place Navigation Sensor #7 at Assembly Platform"}, {"step": 32, "executor": "Human", "action": "Secure Navigation Sensor #7"}]