---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 6m × 4m kitchen with distinct cooking and storage zones. Fragile upper cabinets and lower heavy-duty drawers.
- Goal: Store all items in temperature-appropriate zones (perishables in fridge, dry goods in pantry at (5.8,3.5)).

[Agents]
- Human: Starts at (1,2) near refrigerator. Robot: Starts at (4,1) near sink. Both can navigate freely.

[Interactable Objects]
['Glass Jar', 'Ceramic Bowl', 'Milk Carton', 'Bag of Rice', 'Cast Iron Pan', 'Vegetables', 'Metal Ladle']
[Human Preferences]
1. I prefer to handle all fragile items, such as the Glass Jar #1 and Ceramic Bowl #2, myself to minimize the risk of breakage.

2. I want the robot to place all perishable items in the refrigerator first, ensuring freshness, starting with the Milk Carton #3.

3. I prefer to prioritize organizing the pantry at (5.8, 3.5) with dry goods like the Bag of Rice #4 before other non-refrigerated storage to optimize meal prep efficiency.

4. I like to work in a clockwise direction in the kitchen starting from my initial position, so I’ll direct tasks starting with items closest to me on the left.

5. I prefer the robot to adjust any heavy items like the Cast Iron Pan #5 into the storage zone first, to free up space and reduce clutter in the work area.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

