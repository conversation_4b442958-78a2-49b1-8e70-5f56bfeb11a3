#!/usr/bin/env python3
"""
Mistral模型测试脚本 - 验证模型是否正常工作
"""

import torch
import logging
from transformers import AutoTokenizer, AutoModelForCausalLM
import argparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_loading(model_path):
    """测试模型加载"""
    try:
        logger.info(f"测试加载模型: {model_path}")
        
        # 加载分词器
        logger.info("加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        logger.info("✅ 分词器加载成功")
        
        # 加载模型
        logger.info("加载模型...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        logger.info("✅ 模型加载成功")
        
        return model, tokenizer
        
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None, None

def test_generation(model, tokenizer):
    """测试文本生成"""
    try:
        logger.info("测试文本生成...")
        
        # 测试prompt
        test_prompt = "<s>[INST] Generate a simple human-robot collaboration plan for assembling a toy car. [/INST] "
        
        # 编码输入
        inputs = tokenizer(test_prompt, return_tensors="pt")
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        # 生成文本
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=512,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        
        # 解码输出
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取回复部分
        if "[/INST] " in response:
            response = response.split("[/INST] ", 1)[1]
        
        logger.info("✅ 文本生成成功")
        logger.info(f"生成的文本:\n{response}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 文本生成失败: {e}")
        return False

def test_mistral_format():
    """测试Mistral对话格式"""
    logger.info("测试Mistral对话格式...")
    
    # 测试格式化函数
    def format_mistral_prompt(instruction, input_text=""):
        if input_text.strip():
            return f"<s>[INST] {instruction}\n\n{input_text} [/INST] "
        else:
            return f"<s>[INST] {instruction} [/INST] "
    
    # 测试用例
    test_cases = [
        ("Hello", ""),
        ("Explain AI", "What is artificial intelligence?"),
        ("Plan collaboration", "Robot and human need to assemble a device")
    ]
    
    for instruction, input_text in test_cases:
        formatted = format_mistral_prompt(instruction, input_text)
        logger.info(f"格式化结果: {formatted}")
    
    logger.info("✅ 格式测试完成")

def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU内存: {gpu_memory:.1f} GB")
        
        if gpu_memory < 6:
            logger.warning("⚠️ GPU内存可能不足，建议使用4-bit量化")
        else:
            logger.info("✅ GPU内存充足")
    else:
        logger.warning("⚠️ 未检测到GPU，将使用CPU (速度较慢)")

def main():
    parser = argparse.ArgumentParser(description="测试Mistral模型")
    parser.add_argument("--model", default="mistralai/Mistral-7B-Instruct-v0.3", 
                       help="模型路径或名称")
    parser.add_argument("--skip_generation", action="store_true", 
                       help="跳过文本生成测试")
    
    args = parser.parse_args()
    
    print("=== Mistral模型测试 ===")
    
    # 检查GPU
    check_gpu_memory()
    
    # 测试格式
    test_mistral_format()
    
    # 测试模型加载
    model, tokenizer = test_model_loading(args.model)
    
    if model is None or tokenizer is None:
        print("\n❌ 模型加载失败，请检查:")
        print("1. 网络连接是否正常")
        print("2. 模型路径是否正确")
        print("3. 是否有足够的磁盘空间")
        print("4. 是否需要Hugging Face token")
        return
    
    # 测试文本生成
    if not args.skip_generation:
        success = test_generation(model, tokenizer)
        if not success:
            print("\n❌ 文本生成失败，可能原因:")
            print("1. GPU内存不足")
            print("2. 模型权重损坏")
            print("3. CUDA版本不兼容")
            return
    
    print("\n✅ 所有测试通过!")
    print("现在可以开始训练:")
    print("python run_training.py")

if __name__ == "__main__":
    main()
