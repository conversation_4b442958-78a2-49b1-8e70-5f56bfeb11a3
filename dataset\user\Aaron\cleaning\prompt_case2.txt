---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5m × 5m living room with a couch, coffee table, and entertainment center. The floor is carpeted.
- Goal: Vacuum the carpet, dust the shelves, and organize loose items on the coffee table.

[Agents]
- Human: Starts at (0,0). Robot: Starts at (4,4). Both can navigate freely.

[Interactable Objects]
['Vacuum Cleaner', 'Dusting Cloth', 'Surface Spray', 'Trash Bag', 'Upholstery Brush', 'Furniture Polish', 'Disposable Gloves']
[Human Preferences]
1. I prefer to start by vacuuming the carpet before dusting or organizing, to avoid spreading dust onto freshly cleaned surfaces.

2. I prefer to work clockwise in the room to ensure no spot is missed. Starting from the human’s initial position at (0,0), moving towards the vacuum cleaner at (0.55, 2.72) first.

3. I prefer to have quick and open access to the current tool being used, so I will prioritize picking up the vacuum cleaner, dusting cloth, and upholstery brush in the order they are closest to my path.

4. I prefer to use the robot to assist with carrying cleaning supplies from one task location to another to speed up the process.

5. I prefer to organize loose items on the coffee table as the final step to ensure the area looks neat once all cleaning tasks are completed.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

