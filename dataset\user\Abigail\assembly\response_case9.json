[{"step": 0, "executor": "Human", "action": "Move to Cooling Plate (0,6 → 0,0)"}, {"step": 0, "executor": "Robot", "action": "Move to Fuse Assembly (6,6 → 6,0)"}, {"step": 1, "executor": "Human", "action": "Pick up Cooling Plate"}, {"step": 2, "executor": "Robot", "action": "Pick up Fuse Assembly"}, {"step": 3, "executor": "Human", "action": "Move to Power Core (0,0 → 3,0)"}, {"step": 4, "executor": "Robot", "action": "Move to Charging Port (6,0 → 3,0)"}, {"step": 5, "executor": "Human", "action": "Pick up Power Core"}, {"step": 6, "executor": "Robot", "action": "Pick up Charging Port"}, {"step": 7, "executor": "Human", "action": "Move to Control Board (3,0 → 3,3)"}, {"step": 8, "executor": "Robot", "action": "Move to Thermal Sensor (3,0 → 0,0)"}, {"step": 9, "executor": "Human", "action": "Pick up Control Board"}, {"step": 10, "executor": "Robot", "action": "Pick up Thermal Sensor"}, {"step": 11, "executor": "Human", "action": "Assemble Power Core and Control Board"}, {"step": 12, "executor": "Robot", "action": "Move to Battery Casing (0,0 → 6,6)"}, {"step": 13, "executor": "Human", "action": "Assemble Cooling Plate"}, {"step": 14, "executor": "Robot", "action": "Pick up Battery Casing"}, {"step": 15, "executor": "Human", "action": "Assemble Fuse Assembly and Charging Port"}, {"step": 16, "executor": "Robot", "action": "Move to Assembly Point (6,6 → 3,3)"}, {"step": 17, "executor": "Human", "action": "Assemble Thermal Sensor"}, {"step": 18, "executor": "Robot", "action": "Handover Battery Casing to Human"}, {"step": 19, "executor": "Human", "action": "Final Assembly of Battery Pack"}]