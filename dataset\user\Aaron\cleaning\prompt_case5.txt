---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A compact 4m × 4m office space with a desk, chair, and shelves. Documents must be kept dry.
- Goal: Wipe down the desk, clean the chair, and organize loose papers on the shelves.

[Agents]
- Human: Starts at (0,0). Robot: Starts at (3,3). The robot cannot climb onto furniture.

[Interactable Objects]
['Antibacterial Wipes', 'Paper Towels', 'Compressed Air', 'Trash Bin', 'Desktop Organizer', 'Keyboard Cleaner Gel', 'Furniture Polish']
[Human Preferences]
1. I prefer to use antibacterial wipes first to clean the desk and chair, as hygiene is a priority.

2. I prefer to use paper towels instead of furniture polish, as documents need to be kept dry and paper towels minimize the risk of moisture.

3. I prefer the robot to gather and deliver the necessary cleaning supplies (wipes and paper towels) to me to save time and effort.

4. I prefer to organize documents using the desktop organizer to prevent clutter and ensure a clear workspace.

5. I prefer the compressed air and keyboard cleaner gel to be used on electronic devices to avoid unnecessary direct contact with liquids.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

