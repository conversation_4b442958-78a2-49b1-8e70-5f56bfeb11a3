#!/usr/bin/env python3
"""
训练启动脚本 - 简化版本，直接设置参数
"""

import os
import sys
import torch
import logging
from train_lora import main
import transformers

def setup_environment():
    """设置训练环境"""
    
    # 设置CUDA相关环境变量
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 使用第一块GPU
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    # 设置transformers缓存目录
    os.environ["HF_HOME"] = "./cache"
    os.environ["TRANSFORMERS_CACHE"] = "./cache"
    
    # 创建必要的目录
    os.makedirs("./cache", exist_ok=True)
    os.makedirs("./output", exist_ok=True)
    os.makedirs("./logs", exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
        handlers=[
            logging.FileHandler("./logs/training.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_requirements():
    """检查依赖包"""
    required_packages = [
        "torch",
        "transformers",
        "datasets", 
        "peft",
        "bitsandbytes",
        "accelerate",
        "tensorboard"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少以下依赖包: {missing_packages}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main_with_args():
    """使用预设参数运行训练"""
    
    # 设置命令行参数
    sys.argv = [
        "run_training.py",
        "--model_name_or_path", "deepseek-ai/DeepSeek-V2",
        "--data_path", "dataset/alpaca/all_alpaca_data.json",
        "--output_dir", "./output/deepseek-v2-lora-human-robot-collaboration",
        "--overwrite_output_dir",
        "--do_train",
        "--do_eval",
        "--evaluation_strategy", "steps",
        "--eval_steps", "100",
        "--save_strategy", "steps", 
        "--save_steps", "500",
        "--save_total_limit", "3",
        "--load_best_model_at_end",
        "--metric_for_best_model", "eval_loss",
        "--num_train_epochs", "3",
        "--per_device_train_batch_size", "2",
        "--per_device_eval_batch_size", "2", 
        "--gradient_accumulation_steps", "8",
        "--learning_rate", "2e-4",
        "--weight_decay", "0.01",
        "--warmup_ratio", "0.03",
        "--lr_scheduler_type", "cosine",
        "--logging_steps", "10",
        "--dataloader_num_workers", "4",
        "--remove_unused_columns", "False",
        "--report_to", "tensorboard",
        "--model_max_length", "4096",
        "--use_lora",
        "--lora_r", "16",
        "--lora_alpha", "32", 
        "--lora_dropout", "0.1",
        "--lora_target_modules", "q_proj,v_proj,k_proj,o_proj,gate_proj,up_proj,down_proj",
        "--use_4bit",
        "--fp16",
        "--gradient_checkpointing",
        "--optim", "adamw_torch",
        "--seed", "42",
        "--trust_remote_code"
    ]
    
    # 运行训练
    main()

if __name__ == "__main__":
    print("=== DeepSeek-V2 LoRA训练启动 ===")
    
    # 检查环境
    print("1. 设置训练环境...")
    setup_environment()
    
    print("2. 检查依赖包...")
    if not check_requirements():
        sys.exit(1)
    
    print("3. 检查GPU可用性...")
    if torch.cuda.is_available():
        print(f"   GPU可用: {torch.cuda.get_device_name(0)}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("   警告: 未检测到GPU，将使用CPU训练（速度较慢）")
    
    print("4. 检查数据文件...")
    data_path = "dataset/alpaca/all_alpaca_data.json"
    if os.path.exists(data_path):
        print(f"   数据文件存在: {data_path}")
    else:
        print(f"   错误: 数据文件不存在: {data_path}")
        print("   请先运行 convert_to_alpaca.py 生成训练数据")
        sys.exit(1)
    
    print("5. 开始训练...")
    try:
        main_with_args()
        print("\n=== 训练完成 ===")
    except Exception as e:
        print(f"\n=== 训练失败 ===")
        print(f"错误信息: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
