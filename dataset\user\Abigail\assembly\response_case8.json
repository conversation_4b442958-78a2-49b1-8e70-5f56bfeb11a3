[{"step": 0, "executor": "Robot", "action": "Pick up 'Control Switch'"}, {"step": 0, "executor": "Human", "action": "Pick up 'Pressure Gauge'"}, {"step": 1, "executor": "Robot", "action": "Pick up 'Valve System'"}, {"step": 1, "executor": "Human", "action": "Pick up 'Reservoir Tank'"}, {"step": 2, "executor": "Robot", "action": "Move to (4,3) for handoff"}, {"step": 2, "executor": "Human", "action": "Move to (2,6) for handoff"}, {"step": 3, "executor": "Robot", "action": "Hand off 'Control Switch' to Human"}, {"step": 3, "executor": "Human", "action": "Receive 'Control Switch' from Robot"}, {"step": 4, "executor": "Robot", "action": "Hand off 'Valve System' to Human"}, {"step": 4, "executor": "Human", "action": "Receive 'Valve System' from Robot"}, {"step": 5, "executor": "Human", "action": "Assemble 'Control Switch' and 'Valve System'"}, {"step": 5, "executor": "Robot", "action": "Move to (4,6) to pick up 'Hydraulic Pump'"}, {"step": 6, "executor": "Robot", "action": "Pick up 'Hydraulic Pump'"}, {"step": 6, "executor": "Human", "action": "Move to (2,3) for assembly"}, {"step": 7, "executor": "Robot", "action": "Move to (4,3) for handoff"}, {"step": 7, "executor": "Human", "action": "Assemble 'Pressure Gauge' and 'Reservoir Tank'"}, {"step": 8, "executor": "Robot", "action": "Hand off 'Hydraulic Pump' to Human"}, {"step": 8, "executor": "Human", "action": "Receive 'Hydraulic Pump' from Robot"}, {"step": 9, "executor": "Human", "action": "Assemble 'Hydraulic Pump'"}, {"step": 9, "executor": "Robot", "action": "Move to (4,6) to pick up 'Press Base'"}, {"step": 10, "executor": "Robot", "action": "Pick up 'Press Base'"}, {"step": 11, "executor": "Robot", "action": "Move to (4,3) for handoff"}, {"step": 11, "executor": "Human", "action": "Move to (2,3) for handoff"}, {"step": 12, "executor": "Robot", "action": "Hand off 'Press Base' to Human"}, {"step": 12, "executor": "Human", "action": "Receive 'Press Base' from Robot"}, {"step": 13, "executor": "Human", "action": "Assemble 'Press Base'"}, {"step": 13, "executor": "Robot", "action": "Move to (4,6) to pick up 'Cylinder Unit'"}, {"step": 14, "executor": "Robot", "action": "Pick up 'Cylinder Unit'"}, {"step": 15, "executor": "Robot", "action": "Move to (4,3) for handoff"}, {"step": 15, "executor": "Human", "action": "Move to (2,3) for handoff"}, {"step": 16, "executor": "Robot", "action": "Hand off 'Cylinder Unit' to Human"}, {"step": 16, "executor": "Human", "action": "Receive 'Cylinder Unit' from Robot"}, {"step": 17, "executor": "Human", "action": "Assemble 'Cylinder Unit'"}]