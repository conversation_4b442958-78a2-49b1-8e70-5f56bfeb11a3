#!/usr/bin/env python3
"""
环境设置脚本 - 安装训练所需的依赖包
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    print("✓ Python版本符合要求")
    return True

def check_cuda():
    """检查CUDA可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA可用: {torch.version.cuda}")
            print(f"✓ GPU设备: {torch.cuda.get_device_name(0)}")
            return True
        else:
            print("⚠ CUDA不可用，将使用CPU训练")
            return False
    except ImportError:
        print("⚠ PyTorch未安装，无法检查CUDA")
        return False

def install_pytorch():
    """安装PyTorch"""
    system = platform.system().lower()
    
    if system == "windows":
        # Windows CUDA安装
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    elif system == "linux":
        # Linux CUDA安装
        command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    else:
        # macOS或其他系统
        command = "pip install torch torchvision torchaudio"
    
    return run_command(command, "安装PyTorch")

def install_requirements():
    """安装requirements.txt中的依赖"""
    if not os.path.exists("requirements.txt"):
        print("错误: requirements.txt文件不存在")
        return False
    
    command = "pip install -r requirements.txt"
    return run_command(command, "安装其他依赖包")

def install_flash_attention():
    """安装Flash Attention (可选)"""
    print("\n是否安装Flash Attention? (可以显著加速训练，但需要编译)")
    print("注意: 需要CUDA和兼容的GPU")
    
    choice = input("输入 y/n (默认n): ").lower().strip()
    
    if choice == 'y' or choice == 'yes':
        command = "pip install flash-attn --no-build-isolation"
        success = run_command(command, "安装Flash Attention")
        if not success:
            print("⚠ Flash Attention安装失败，将使用标准attention")
        return success
    else:
        print("跳过Flash Attention安装")
        return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "cache",
        "output", 
        "logs",
        "checkpoints"
    ]
    
    print("\n创建必要目录...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ 创建目录: {directory}")

def verify_installation():
    """验证安装"""
    print("\n验证安装...")
    
    packages_to_check = [
        "torch",
        "transformers", 
        "datasets",
        "peft",
        "bitsandbytes",
        "accelerate"
    ]
    
    failed_packages = []
    
    for package in packages_to_check:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n以下包安装失败: {failed_packages}")
        return False
    else:
        print("\n✓ 所有核心包安装成功!")
        return True

def main():
    """主函数"""
    print("=== DeepSeek-V2 LoRA训练环境设置 ===")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 升级pip
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 安装PyTorch
    if not install_pytorch():
        print("PyTorch安装失败，请手动安装")
        sys.exit(1)
    
    # 检查CUDA
    check_cuda()
    
    # 安装其他依赖
    if not install_requirements():
        print("依赖包安装失败")
        sys.exit(1)
    
    # 安装Flash Attention (可选)
    install_flash_attention()
    
    # 创建目录
    create_directories()
    
    # 验证安装
    if verify_installation():
        print("\n=== 环境设置完成 ===")
        print("现在可以运行以下命令开始训练:")
        print("python run_training.py")
    else:
        print("\n=== 环境设置失败 ===")
        print("请检查错误信息并手动安装失败的包")
        sys.exit(1)

if __name__ == "__main__":
    main()
