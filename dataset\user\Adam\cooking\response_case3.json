[{"step": 0, "executor": "Robot", "action": "In<PERSON> <PERSON><PERSON>"}, {"step": 0, "executor": "Human", "action": "Move to Stove"}, {"step": 1, "executor": "Robot", "action": "Move to Pasta Box"}, {"step": 1, "executor": "Human", "action": "<PERSON>par<PERSON>"}, {"step": 2, "executor": "Human", "action": "Move to Fridge"}, {"step": 3, "executor": "Human", "action": "Retrieve Cheese Grater"}, {"step": 4, "executor": "Human", "action": "Move back to Stove"}, {"step": 5, "executor": "Robot", "action": "Retrieve Pasta Box"}, {"step": 6, "executor": "Robot", "action": "Move to Cooking Pot"}, {"step": 7, "executor": "Robot", "action": "Retrieve Cooking Pot"}, {"step": 8, "executor": "Robot", "action": "Move to Stove"}, {"step": 9, "executor": "Robot", "action": "Place Pasta Box and Cooking Pot near Stove"}, {"step": 10, "executor": "Robot", "action": "Hold Cooking Pot steadily"}, {"step": 11, "executor": "Human", "action": "Verbally confirm readiness for Cooking Pot"}, {"step": 12, "executor": "Robot", "action": "Release Cooking Pot"}, {"step": 13, "executor": "Robot", "action": "Move to Wooden Spoon"}, {"step": 14, "executor": "Robot", "action": "Retrieve <PERSON><PERSON>"}, {"step": 15, "executor": "Robot", "action": "Move to Colander"}, {"step": 16, "executor": "Robot", "action": "Retrieve <PERSON>"}, {"step": 17, "executor": "Robot", "action": "Move to Stove"}, {"step": 18, "executor": "Robot", "action": "Place Wooden Spoon and Colander near Stove"}, {"step": 19, "executor": "Human", "action": "Indicate readiness for <PERSON>en <PERSON> and Colander"}, {"step": 20, "executor": "Robot", "action": "Hand over <PERSON><PERSON> and Colander"}]