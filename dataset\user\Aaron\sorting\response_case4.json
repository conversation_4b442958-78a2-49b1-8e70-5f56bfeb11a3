[{"step": 0, "executor": "Human", "action": "Pick up <PERSON> (Fragile)"}, {"step": 0, "executor": "Robot", "action": "Move to Paper Bag (Compostable)"}, {"step": 1, "executor": "Human", "action": "Place Glass Bottle on Fragile conveyor"}, {"step": 1, "executor": "Robot", "action": "Pick up Paper Bag (Compostable)"}, {"step": 2, "executor": "Human", "action": "Move to Soda Can (Recyclable)"}, {"step": 2, "executor": "Robot", "action": "Place Paper Bag on Compostable conveyor"}, {"step": 3, "executor": "Human", "action": "Pick up Soda Can (Recyclable)"}, {"step": 3, "executor": "Robot", "action": "Move to Plastic Container (Recyclable)"}, {"step": 4, "executor": "Human", "action": "Place Soda Can on Recyclable conveyor"}, {"step": 4, "executor": "Robot", "action": "Pick up Plastic Container (Recyclable)"}, {"step": 5, "executor": "Human", "action": "Move to Bioplastic Cup (Compostable)"}, {"step": 5, "executor": "Robot", "action": "Place Plastic Container on Recyclable conveyor"}, {"step": 6, "executor": "Human", "action": "Pick up Bioplastic Cup (Compostable)"}, {"step": 6, "executor": "Robot", "action": "Move to Large Cardboard (Recyclable)"}, {"step": 7, "executor": "Human", "action": "Place Bioplastic Cup on Compostable conveyor"}, {"step": 7, "executor": "Robot", "action": "Pick up Large Cardboard (Recyclable)"}, {"step": 8, "executor": "Human", "action": "Move to <PERSON> Towel (Compostable)"}, {"step": 8, "executor": "Robot", "action": "Place Large Cardboard on Recyclable conveyor"}, {"step": 9, "executor": "Human", "action": "Pick up Used <PERSON>l (Compostable)"}, {"step": 9, "executor": "Robot", "action": "Move to end of rail"}, {"step": 10, "executor": "Human", "action": "Place Used Towel on Compostable conveyor"}]