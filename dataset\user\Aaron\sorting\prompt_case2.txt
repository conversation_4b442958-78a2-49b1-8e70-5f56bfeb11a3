---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A 5m × 5m warehouse with overhead shelving and designated drop-off points for sorted materials.
- Goal: Organize items based on material type and condition (fragile, hazardous, etc.) onto labeled pallets.

[Agents]
- Robot: Starts at (2.5, 2.5) with full mobility.
Human: Stations at (0, 2.5) and can push carts along the edges.

[Interactable Objects]
['Plastic Bottle (Recyclable)', 'Glass Jar (Fragile)', 'Metal Can (Recyclable)', 'Paper Cup (Compostable)', 'Styrofoam Block (Non-Recyclable)', 'Food Waste (Compostable)', 'Battery Pack (Hazardous)']
[Human Preferences]
1. I prefer the human to prioritize handling hazardous materials to ensure safety in the work environment.

2. I prefer the robot to handle fragile items first to minimize the risk of damage.

3. I prefer the human to use the cart to transport multiple items at once when possible to increase efficiency.

4. I prefer the robot to sort recyclable materials before non-recyclable ones to optimize recycling efforts.

5. I prefer the human to ensure that all labeled pallets are visible and accessible to both the robot and human for ease of sorting.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

