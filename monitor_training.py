#!/usr/bin/env python3
"""
训练监控脚本 - 监控训练进度和GPU使用情况
"""

import os
import time
import torch
import psutil
import json
from datetime import datetime

def check_gpu_usage():
    """检查GPU使用情况"""
    if torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / 1024**3
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_utilization = (gpu_memory_used / gpu_memory_total) * 100
        
        print(f"GPU内存使用: {gpu_memory_used:.1f}GB / {gpu_memory_total:.1f}GB ({gpu_utilization:.1f}%)")
        return gpu_memory_used, gpu_memory_total
    else:
        print("未检测到GPU")
        return 0, 0

def check_training_progress():
    """检查训练进度"""
    output_dir = "./output/deepseek-6.7b-lora-fixed"
    
    # 检查是否有训练日志
    if os.path.exists(output_dir):
        print(f"输出目录存在: {output_dir}")
        
        # 列出目录内容
        files = os.listdir(output_dir)
        if files:
            print(f"输出文件: {files}")
        else:
            print("输出目录为空")
    else:
        print("输出目录不存在")
    
    # 检查是否有检查点
    checkpoint_dirs = [d for d in os.listdir(".") if d.startswith("checkpoint-") and os.path.isdir(d)]
    if checkpoint_dirs:
        print(f"发现检查点: {checkpoint_dirs}")
    
    return len(checkpoint_dirs) > 0

def check_process_status():
    """检查训练进程状态"""
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
        try:
            if proc.info['name'] == 'python.exe' and 'train_fixed.py' in ' '.join(proc.info['cmdline']):
                python_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if python_processes:
        for proc in python_processes:
            print(f"训练进程 PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%, 内存 {proc['memory_info'].rss/1024**3:.1f}GB")
        return True
    else:
        print("未找到训练进程")
        return False

def estimate_training_time():
    """估算训练时间"""
    total_steps = 156  # 从训练输出中看到的
    steps_per_epoch = total_steps // 3  # 3个epochs
    
    print(f"训练配置:")
    print(f"- 总步数: {total_steps}")
    print(f"- 每个epoch步数: {steps_per_epoch}")
    print(f"- 批次大小: 1")
    print(f"- 梯度累积: 16")
    print(f"- 有效批次大小: 16")
    
    # 估算时间（基于6GB显存的经验）
    estimated_time_per_step = 30  # 秒
    total_estimated_time = total_steps * estimated_time_per_step
    
    print(f"估算训练时间:")
    print(f"- 每步约: {estimated_time_per_step}秒")
    print(f"- 总时间约: {total_estimated_time/3600:.1f}小时")

def main():
    """主监控循环"""
    print("=== DeepSeek 6.7B LoRA训练监控 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示训练配置
    estimate_training_time()
    print()
    
    monitor_count = 0
    while True:
        monitor_count += 1
        print(f"\n--- 监控 #{monitor_count} ({datetime.now().strftime('%H:%M:%S')}) ---")
        
        # 检查进程状态
        process_running = check_process_status()
        
        if not process_running:
            print("训练进程已结束")
            break
        
        # 检查GPU使用
        check_gpu_usage()
        
        # 检查训练进度
        has_checkpoints = check_training_progress()
        
        if has_checkpoints:
            print("✅ 训练正在进行，已生成检查点")
        
        # 等待30秒后再次检查
        print("等待30秒后再次检查...")
        time.sleep(30)
        
        # 最多监控2小时
        if monitor_count >= 240:  # 30秒 * 240 = 2小时
            print("监控时间达到2小时，停止监控")
            break

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n监控被用户中断")
    except Exception as e:
        print(f"监控出错: {e}")
