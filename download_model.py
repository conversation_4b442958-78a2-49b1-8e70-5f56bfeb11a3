#!/usr/bin/env python3
"""
模型下载脚本 - 预先下载Mistral模型到本地
"""

import os
import sys
import argparse
from huggingface_hub import snapshot_download, login
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 可用的Mistral模型
AVAILABLE_MODELS = {
    "mistral-7b-instruct": "mistralai/Mistral-7B-Instruct-v0.3",
    "mistral-7b-base": "mistralai/Mistral-7B-v0.1", 
    "mixtral-8x7b": "mistralai/Mixtral-8x7B-Instruct-v0.1",
    "mistral-7b-instruct-v0.2": "mistralai/Mistral-7B-Instruct-v0.2",
}

def setup_environment():
    """设置下载环境"""
    # 创建模型目录
    os.makedirs("./models", exist_ok=True)
    
    # 设置镜像源 (可选)
    mirror = input("是否使用HF镜像源? (y/n, 默认n): ").lower().strip()
    if mirror in ['y', 'yes']:
        os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
        logger.info("已设置HF镜像源")

def check_disk_space(model_name):
    """检查磁盘空间"""
    import shutil
    
    # 估算模型大小
    model_sizes = {
        "mistral-7b-instruct": 15,  # GB
        "mistral-7b-base": 15,
        "mixtral-8x7b": 90,
        "mistral-7b-instruct-v0.2": 15,
    }
    
    required_space = model_sizes.get(model_name, 20)
    free_space = shutil.disk_usage(".")[2] / (1024**3)  # GB
    
    logger.info(f"模型 {model_name} 预计需要 {required_space}GB 空间")
    logger.info(f"当前可用空间: {free_space:.1f}GB")
    
    if free_space < required_space * 1.2:  # 留20%余量
        logger.warning("磁盘空间可能不足!")
        return False
    
    return True

def download_model(model_key, local_dir=None, token=None):
    """下载模型"""
    if model_key not in AVAILABLE_MODELS:
        logger.error(f"未知模型: {model_key}")
        logger.info(f"可用模型: {list(AVAILABLE_MODELS.keys())}")
        return False
    
    model_id = AVAILABLE_MODELS[model_key]
    
    if local_dir is None:
        local_dir = f"./models/{model_key}"
    
    logger.info(f"开始下载模型: {model_id}")
    logger.info(f"保存路径: {local_dir}")
    
    # 检查磁盘空间
    if not check_disk_space(model_key):
        proceed = input("磁盘空间可能不足，是否继续? (y/n): ").lower().strip()
        if proceed not in ['y', 'yes']:
            return False
    
    try:
        # 登录HF (如果需要)
        if token:
            login(token=token)
        
        # 下载模型
        snapshot_download(
            repo_id=model_id,
            local_dir=local_dir,
            local_dir_use_symlinks=False,  # 不使用符号链接
            resume_download=True,  # 支持断点续传
        )
        
        logger.info(f"模型下载完成: {local_dir}")
        
        # 更新配置文件
        update_config_files(local_dir)
        
        return True
        
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return False

def update_config_files(local_model_path):
    """更新配置文件中的模型路径"""
    files_to_update = [
        ("train_lora.py", 'model_name_or_path: Optional[str] = field(default="', '"'),
        ("run_training.py", '"--model_name_or_path", "', '"'),
        ("inference.py", 'default="', '"'),
        ("train_config.yaml", 'model_name_or_path: "', '"'),
    ]
    
    logger.info("更新配置文件中的模型路径...")
    
    for filename, prefix, suffix in files_to_update:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找并替换模型路径
                import re
                pattern = f'{re.escape(prefix)}[^{suffix}]*{re.escape(suffix)}'
                replacement = f'{prefix}{local_model_path}{suffix}'
                
                new_content = re.sub(pattern, replacement, content)
                
                if new_content != content:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    logger.info(f"已更新: {filename}")
                
            except Exception as e:
                logger.warning(f"更新 {filename} 失败: {e}")

def list_models():
    """列出可用模型"""
    print("\n可用的Mistral模型:")
    print("-" * 50)
    
    for key, model_id in AVAILABLE_MODELS.items():
        print(f"{key:20} -> {model_id}")
    
    print("\n推荐:")
    print("- mistral-7b-instruct: 最常用，性能好，显存需求低")
    print("- mixtral-8x7b: 性能最强，但需要更多显存")

def main():
    parser = argparse.ArgumentParser(description="下载Mistral模型")
    parser.add_argument("--model", choices=list(AVAILABLE_MODELS.keys()), 
                       help="要下载的模型")
    parser.add_argument("--local_dir", help="本地保存目录")
    parser.add_argument("--token", help="Hugging Face token (如果需要)")
    parser.add_argument("--list", action="store_true", help="列出可用模型")
    
    args = parser.parse_args()
    
    if args.list:
        list_models()
        return
    
    # 设置环境
    setup_environment()
    
    # 选择模型
    if not args.model:
        print("\n请选择要下载的模型:")
        list_models()
        
        while True:
            model_key = input("\n输入模型名称 (或 'q' 退出): ").strip()
            if model_key.lower() == 'q':
                return
            if model_key in AVAILABLE_MODELS:
                break
            print("无效的模型名称，请重新输入")
    else:
        model_key = args.model
    
    # 下载模型
    success = download_model(
        model_key=model_key,
        local_dir=args.local_dir,
        token=args.token
    )
    
    if success:
        print(f"\n✅ 模型 {model_key} 下载完成!")
        print("现在可以运行训练脚本:")
        print("python run_training.py")
    else:
        print(f"\n❌ 模型 {model_key} 下载失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
