[{"step": 0, "executor": "Robot", "action": "Pick up <PERSON>ather Duster #1"}, {"step": 0, "executor": "Human", "action": "Start moving to Organizer Bin"}, {"step": 1, "executor": "Robot", "action": "Pick up Microfiber Cloth #2"}, {"step": 1, "executor": "Human", "action": "Continue moving to Organizer Bin"}, {"step": 2, "executor": "Robot", "action": "Move to deliver Feather Duster and Microfiber Cloth"}, {"step": 2, "executor": "Human", "action": "Pick up Organizer Bin"}, {"step": 3, "executor": "Human", "action": "Start moving to Trash Bag"}, {"step": 4, "executor": "Robot", "action": "Deliver Feather Duster and Microfiber Cloth"}, {"step": 4, "executor": "Human", "action": "Pick up Trash Bag"}, {"step": 5, "executor": "Human", "action": "Start moving to Table Cleaner"}, {"step": 5, "executor": "Robot", "action": "Move to pick up <PERSON><PERSON><PERSON><PERSON> with Brush Attachment #6"}, {"step": 6, "executor": "Human", "action": "Pick up Table Cleaner"}, {"step": 7, "executor": "Human", "action": "Start moving to Soft Book Cleaner"}, {"step": 7, "executor": "Robot", "action": "Pick up <PERSON><PERSON><PERSON><PERSON> with Brush Attachment #6"}, {"step": 8, "executor": "Human", "action": "Pick up Soft Book Cleaner"}, {"step": 9, "executor": "Human", "action": "Start moving to study table"}, {"step": 9, "executor": "Robot", "action": "Move to start dusting shelves"}, {"step": 10, "executor": "Human", "action": "Clean table surface"}, {"step": 11, "executor": "Human", "action": "Move to next table"}, {"step": 12, "executor": "Human", "action": "Clean table surface"}, {"step": 12, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 13, "executor": "Human", "action": "Organize misplaced books"}, {"step": 14, "executor": "Human", "action": "Move to next shelf"}, {"step": 15, "executor": "Human", "action": "Organize misplaced books"}, {"step": 16, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 16, "executor": "Human", "action": "Move to next shelf"}, {"step": 17, "executor": "Human", "action": "Organize misplaced books"}, {"step": 18, "executor": "Human", "action": "Move to next shelf"}, {"step": 19, "executor": "Human", "action": "Organize misplaced books"}, {"step": 20, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 20, "executor": "Human", "action": "Move to next shelf"}, {"step": 21, "executor": "Human", "action": "Organize misplaced books"}, {"step": 22, "executor": "Human", "action": "Move to next shelf"}, {"step": 23, "executor": "Human", "action": "Organize misplaced books"}, {"step": 24, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 24, "executor": "Human", "action": "Move to next shelf"}, {"step": 25, "executor": "Human", "action": "Organize misplaced books"}, {"step": 26, "executor": "Human", "action": "Move to next shelf"}, {"step": 27, "executor": "Human", "action": "Organize misplaced books"}, {"step": 28, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 28, "executor": "Human", "action": "Move to next shelf"}, {"step": 29, "executor": "Human", "action": "Organize misplaced books"}, {"step": 30, "executor": "Human", "action": "Move to next shelf"}, {"step": 31, "executor": "Human", "action": "Organize misplaced books"}, {"step": 32, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 32, "executor": "Human", "action": "Move to next shelf"}, {"step": 33, "executor": "Human", "action": "Organize misplaced books"}, {"step": 34, "executor": "Human", "action": "Move to next shelf"}, {"step": 35, "executor": "Human", "action": "Organize misplaced books"}, {"step": 36, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 36, "executor": "Human", "action": "Move to next shelf"}, {"step": 37, "executor": "Human", "action": "Organize misplaced books"}, {"step": 38, "executor": "Human", "action": "Move to next shelf"}, {"step": 39, "executor": "Human", "action": "Organize misplaced books"}, {"step": 40, "executor": "Robot", "action": "Continue dusting shelves"}, {"step": 40, "executor": "Human", "action": "Move to next shelf"}, {"step": 41, "executor": "Human", "action": "Organize misplaced books"}, {"step": 42, "executor": "Human", "action": "Move to next shelf"}, {"step": 43, "executor": "Human", "action": "Organize misplaced books"}, {"step": 44, "executor": "Human", "action": "Move to next shelf"}, {"step": 45, "executor": "Human", "action": "Organize misplaced books"}, {"step": 46, "executor": "Human", "action": "Move to next shelf"}, {"step": 47, "executor": "Human", "action": "Organize misplaced books"}, {"step": 48, "executor": "Human", "action": "Move to next shelf"}, {"step": 49, "executor": "Human", "action": "Organize misplaced books"}, {"step": 50, "executor": "Human", "action": "Move to next shelf"}, {"step": 51, "executor": "Human", "action": "Organize misplaced books"}]