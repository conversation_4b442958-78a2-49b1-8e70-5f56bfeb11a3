1. I prefer to gather nearby components like the Arm Base #1 and Joint Motor #2 myself, as they are closer to my starting position, while asking the robot to handle items farther away.

2. I want the robot to bring me multiple items at once if it's efficient, but only if it can carry them safely without risking damage to any fragile components.

3. I prefer the robot to organize items in the order they are needed for assembly and provide me with real-time updates on their status so I can plan my movements efficiently.

4. I want to minimize movement by coordinating with the robot to ensure we are both utilizing our maximum efficiency, with me focusing on assembling and the robot on fetching.

5. I prefer to work on one corner of the workstation while the robot supplies parts in an adjacent corner to minimize crossover and potential interference.