---
You are an intelligent planning agent specializing in human-robot collaboration. Your objective is to generate an optimal, step-by-step plan that coordinates a desktop robot and a human to complete a given task in the shortest possible time. You must fully respect all environmental constraints, robot capabilities, human movement limitations, and—most importantly—human’s preferences. In exploring possible solutions, consider both sequential and parallel actions (including synchronous, asynchronous, mutually exclusive, blocking, and non-blocking actions). Strive to produce the plan that satisfies all conditions while minimizing the overall task completion time.
[Scenario]
- A spacious 10m × 6m workshop with multiple workstations. Some surfaces have metal shavings that need careful handling.
- Goal: Clean the workstations, remove metal shavings, and store tools in designated racks.

[Agents]
- Human: Starts at (2,3). Robot: Starts at (8,5). The robot must avoid water spills.

[Interactable Objects]
['Industrial Vacuum', 'Magnetic Sweeper', 'Heavy-Duty Gloves', 'Degreaser', 'Rag Cloth', 'Dustpan and Brush', 'Protective Mask']
[Human Preferences]
1. I prefer to start by putting on the Heavy-Duty Gloves (#3) and Protective Mask (#7) before handling any tasks to ensure safety against metal shavings.

2. I prefer to remove all metal shavings using the Magnetic Sweeper (#2) before moving on to clean other surfaces to minimize the risk of cuts or injuries.

3. I prefer to use the Degreaser (#4) immediately after removing metal shavings to effectively clean the surfaces before storing tools.

4. I prefer to store tools in the designated racks immediately after cleaning each workstation to ensure the workspace remains organized and clutter-free.

5. I prefer to use the Industrial Vacuum (#1) last to ensure no residual shavings or debris are left on the floor, keeping the workshop safe and clean.
[Actions and Timings]
- Robot Actions:
   - move: 0.5 m/s
   - non-moving actions: 2 second each
- Human Actions:
   - move: 1 m/s
   - non-moving actions: 1 second each

**Instructions:**

1. **Optimize for Efficiency:**  
   - Explore every possible solution, including sequential and parallel actions. Ensure that every action (whether performed by the robot or human) adheres to the specified capabilities, movement constraints, and preferences.
   - Aim for the fastest overall task completion time.

2. **Structured Output:**  
   - Present your plan as an organized timeline using the following table structure. **Do not deviate from this format** (though feel free to add clarifying notes for overlapping or parallel actions):

     ### Final Timeline  
     | Time Interval (s) | Executor  | Action                                      | Position Change               |
     |-------------------|-----------|---------------------------------------------|-------------------------------|
     | [Start-End]       | [Agent]   | [Detailed description of the action taken]  | [From position → To position] |

   - If actions occur in parallel, indicate overlapping time intervals and provide clarification as needed (e.g., “(parallel with …)”).

3. **Justify Your Decisions:**  
   - For each action in the timeline, briefly justify why it is necessary given the constraints and human's preferences.
   - If multiple viable options exist, choose the one that minimizes overall task time while adhering to the specified conditions.

---

**Your Task:**  
Based on the detailed information provided above, generate a detailed, step-by-step collaboration plan that satisfies all the requirements. The plan must be structured in the table format shown above, capturing all action sequences (including any potential parallel actions) necessary to accomplish the task in the shortest possible time.

Begin your plan now.

